/**
 * T-Worktime Assistant Content Script
 * Runs on T-Worktime pages to extract employee data and context
 */

class WorktimeContentScript {
    constructor() {
        this.context = null;
        this.init();
    }

    init() {
        // Wait for page to fully load
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.extractContext());
        } else {
            this.extractContext();
        }

        // Listen for messages from popup
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            if (request.action === 'getEmployeeData') {
                this.getEmployeeData().then(sendResponse);
                return true; // Keep message channel open for async response
            }
        });
    }

    extractContext() {
        try {
            console.log('Starting context extraction...');

            // Method 1: Check if context is available as a global variable
            if (window.context) {
                this.context = window.context;
                console.log('T-Worktime context found as global variable:', this.context);
                return;
            }

            // Method 2: Check for common T-Worktime global variables
            const globalVars = ['context', 'empleadoContext', 'userContext', 'appContext'];
            for (const varName of globalVars) {
                if (window[varName] && typeof window[varName] === 'object') {
                    console.log(`Found global variable ${varName}:`, window[varName]);
                    if (window[varName].IdEmpleado) {
                        this.context = window[varName];
                        console.log('T-Worktime context found as', varName, ':', this.context);
                        return;
                    }
                }
            }

            // Method 3: Extract from script tags with improved patterns
            const scripts = document.querySelectorAll('script');
            console.log(`Searching through ${scripts.length} script tags...`);

            for (let i = 0; i < scripts.length; i++) {
                const script = scripts[i];
                const content = script.textContent || script.innerHTML;

                // Skip empty scripts
                if (!content || content.trim().length === 0) continue;

                // Look for context variable declaration with more flexible patterns
                if (content.includes('IdEmpleado')) {
                    console.log(`Found IdEmpleado in script ${i}, analyzing...`);

                    // Try different patterns to extract context
                    const patterns = [
                        /var\s+context\s*=\s*({[^;]+});/s,
                        /context\s*=\s*({[^;]+});/s,
                        /window\.context\s*=\s*({[^;]+});/s,
                        /"context"\s*:\s*({[^}]+})/s,
                        /context\s*=\s*({[\s\S]*?});/s,
                        // More flexible pattern for multiline JSON
                        /context\s*=\s*({[\s\S]*?})\s*;/s
                    ];

                    for (const pattern of patterns) {
                        const match = content.match(pattern);
                        if (match) {
                            console.log('Found potential context match:', match[1].substring(0, 200) + '...');
                            try {
                                this.context = JSON.parse(match[1]);
                                console.log('T-Worktime context extracted from script:', this.context);
                                return;
                            } catch (e) {
                                console.warn('Failed to parse context JSON:', e, 'Content:', match[1].substring(0, 100));
                            }
                        }
                    }
                }
            }

            // Method 3: Look for context in inline scripts with different formats
            for (const script of scripts) {
                const content = script.textContent || script.innerHTML;
                
                // Look for JSON-like structures containing IdEmpleado
                const jsonMatches = content.match(/{[^{}]*"?IdEmpleado"?[^{}]*}/g);
                if (jsonMatches) {
                    for (const jsonMatch of jsonMatches) {
                        try {
                            const parsed = JSON.parse(jsonMatch);
                            if (parsed.IdEmpleado) {
                                this.context = parsed;
                                console.log('T-Worktime context found in JSON structure:', this.context);
                                return;
                            }
                        } catch (e) {
                            // Continue searching
                        }
                    }
                }
            }

            console.warn('T-Worktime context not found on page');
        } catch (error) {
            console.error('Error extracting T-Worktime context:', error);
        }
    }

    async getEmployeeData() {
        try {
            // If context wasn't found during init, try again
            if (!this.context) {
                this.extractContext();
            }

            if (!this.context) {
                throw new Error('Context not available. Make sure you are on the T-Worktime calendar page.');
            }

            const employeeId = this.context.IdEmpleado;
            if (!employeeId) {
                throw new Error('Employee ID not found in context');
            }

            // Extract absent days from Agenda
            const absentDays = [];
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            if (this.context.Agenda && Array.isArray(this.context.Agenda)) {
                this.context.Agenda.forEach(day => {
                    if (day.Trabajable === false) {
                        try {
                            const dayDate = new Date(day.Fecha);
                            dayDate.setHours(0, 0, 0, 0);
                            
                            // Only include future absent days or today
                            if (dayDate >= today) {
                                const dateStr = day.Fecha.split('T')[0]; // Get YYYY-MM-DD format
                                absentDays.push(dateStr);
                            }
                        } catch (e) {
                            console.warn('Error parsing date:', day.Fecha, e);
                        }
                    }
                });
            }

            const result = {
                success: true,
                employeeId: employeeId,
                absentDays: absentDays,
                contextAvailable: true
            };

            console.log('Employee data extracted:', result);
            return result;

        } catch (error) {
            console.error('Error getting employee data:', error);
            return {
                success: false,
                error: error.message,
                employeeId: null,
                absentDays: [],
                contextAvailable: false
            };
        }
    }

    // Helper method to check if we're on the right page
    isWorktimePage() {
        return window.location.hostname.includes('t-worktime.t-systems.es') &&
               (window.location.pathname.includes('Calendario') || 
                window.location.pathname.includes('CalendarioEmpleado'));
    }

    // Method to inject the worktime API if needed
    injectWorktimeAPI() {
        if (document.getElementById('worktime-api-script')) {
            return; // Already injected
        }

        const script = document.createElement('script');
        script.id = 'worktime-api-script';
        script.src = chrome.runtime.getURL('lib/worktime-api.js');
        script.onload = () => {
            console.log('Worktime API script injected successfully');
        };
        script.onerror = () => {
            console.error('Failed to inject Worktime API script');
        };

        (document.head || document.documentElement).appendChild(script);
    }

    // Method to get current page info
    getPageInfo() {
        return {
            url: window.location.href,
            title: document.title,
            isWorktimePage: this.isWorktimePage(),
            contextAvailable: !!this.context,
            employeeId: this.context?.IdEmpleado || null
        };
    }
}

// Initialize content script
const worktimeContent = new WorktimeContentScript();

// Make it available globally for debugging
window.worktimeContent = worktimeContent;
