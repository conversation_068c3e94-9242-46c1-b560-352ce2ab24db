/**
 * T-Worktime Assistant Content Script
 * Runs on T-Worktime pages to extract employee data and context
 */

class WorktimeContentScript {
    constructor() {
        this.context = null;
        this.init();
    }

    init() {
        // Wait for page to fully load and context to be available
        this.waitForContext();

        // Listen for messages from popup
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            if (request.action === 'getEmployeeData') {
                this.getEmployeeData().then(sendResponse);
                return true; // Keep message channel open for async response
            }
        });
    }

    waitForContext() {
        // Try multiple times with delays to ensure context is loaded
        const maxAttempts = 5; // Reduced from 10
        let attempts = 0;

        const tryExtract = () => {
            attempts++;
            // Only log on first and last attempts to reduce noise
            if (attempts === 1) {
                console.log('🔍 T-Worktime: Starting context extraction...');
            }

            this.extractContext();

            if (!this.context && attempts < maxAttempts) {
                // Wait a bit longer and try again
                setTimeout(tryExtract, 500);
            } else if (this.context) {
                console.log(`✅ T-Worktime: Context successfully extracted after ${attempts} attempt(s)`);
            } else {
                console.warn(`❌ T-Worktime: Failed to extract context after ${maxAttempts} attempts`);
            }
        };

        // Start immediately if page is already loaded
        if (document.readyState === 'complete') {
            setTimeout(tryExtract, 100); // Small delay to ensure scripts have run
        } else {
            // Wait for page load
            window.addEventListener('load', () => {
                setTimeout(tryExtract, 100);
            });
        }
    }

    extractContext() {
        try {
            // Method 1: Check if context is available as a global variable
            if (window.context && typeof window.context === 'object') {
                this.context = window.context;
                console.log('✅ T-Worktime context found as window.context');
                console.log('Employee ID:', this.context.IdEmpleado);
                console.log('Employee Name:', this.context.Nombre);
                console.log('Agenda items:', this.context.Agenda ? this.context.Agenda.length : 'none');
                return;
            }

            // Method 2: Check other known variables that contain employee data
            const candidateVars = ['regDefaultJL', 'regDefaultPC'];
            for (const varName of candidateVars) {
                if (window[varName] && window[varName].IdEmpleado) {
                    console.log(`✅ Found employee data in window.${varName}`);
                    // These don't have Agenda data, but we can use them as fallback
                    if (!this.context) {
                        this.context = {
                            IdEmpleado: window[varName].IdEmpleado,
                            Agenda: [] // Empty agenda as fallback
                        };
                        console.log('Using fallback context from', varName);
                        return;
                    }
                }
            }

            // Method 3: Try to find context in script tags (this usually works)
            console.log('🔍 Searching for context in script tags...');

            // Method 3: Look for context in inline scripts as fallback
            const scripts = document.querySelectorAll('script');

            for (const script of scripts) {
                const content = script.textContent || script.innerHTML;

                if (content && content.includes('IdEmpleado')) {
                    // Look for JSON-like structures containing IdEmpleado
                    const jsonMatches = content.match(/{[^{}]*"?IdEmpleado"?[^{}]*}/g);
                    if (jsonMatches) {
                        for (const jsonMatch of jsonMatches) {
                            try {
                                const parsed = JSON.parse(jsonMatch);
                                if (parsed.IdEmpleado) {
                                    this.context = parsed;
                                    console.log('✅ T-Worktime context found in script tag');
                                    console.log('Employee ID:', parsed.IdEmpleado);
                                    return;
                                }
                            } catch (e) {
                                // Continue searching
                            }
                        }
                    }
                }
            }

            console.warn('❌ T-Worktime context not found - make sure you are on the calendar page and logged in');
        } catch (error) {
            console.error('Error extracting T-Worktime context:', error);
        }
    }

    async getEmployeeData() {
        try {
            // If context wasn't found during init, try again
            if (!this.context) {
                this.extractContext();
            }

            if (!this.context) {
                throw new Error('Context not available. Make sure you are on the T-Worktime calendar page.');
            }

            const employeeId = this.context.IdEmpleado;
            if (!employeeId) {
                throw new Error('Employee ID not found in context');
            }

            // Extract absent days from Agenda
            const absentDays = [];
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            if (this.context.Agenda && Array.isArray(this.context.Agenda)) {
                this.context.Agenda.forEach(day => {
                    if (day.Trabajable === false) {
                        try {
                            const dayDate = new Date(day.Fecha);
                            dayDate.setHours(0, 0, 0, 0);
                            
                            // Only include future absent days or today
                            if (dayDate >= today) {
                                const dateStr = day.Fecha.split('T')[0]; // Get YYYY-MM-DD format
                                absentDays.push(dateStr);
                            }
                        } catch (e) {
                            console.warn('Error parsing date:', day.Fecha, e);
                        }
                    }
                });
            }

            const result = {
                success: true,
                employeeId: employeeId,
                absentDays: absentDays,
                contextAvailable: true
            };

            console.log('Employee data extracted:', result);
            return result;

        } catch (error) {
            console.error('Error getting employee data:', error);
            return {
                success: false,
                error: error.message,
                employeeId: null,
                absentDays: [],
                contextAvailable: false
            };
        }
    }

    // Helper method to check if we're on the right page
    isWorktimePage() {
        return window.location.hostname.includes('t-worktime.t-systems.es') &&
               (window.location.pathname.includes('Calendario') || 
                window.location.pathname.includes('CalendarioEmpleado'));
    }

    // Method to inject the worktime API if needed
    injectWorktimeAPI() {
        if (document.getElementById('worktime-api-script')) {
            return; // Already injected
        }

        const script = document.createElement('script');
        script.id = 'worktime-api-script';
        script.src = chrome.runtime.getURL('lib/worktime-api.js');
        script.onload = () => {
            console.log('Worktime API script injected successfully');
        };
        script.onerror = () => {
            console.error('Failed to inject Worktime API script');
        };

        (document.head || document.documentElement).appendChild(script);
    }

    // Method to get current page info
    getPageInfo() {
        return {
            url: window.location.href,
            title: document.title,
            isWorktimePage: this.isWorktimePage(),
            contextAvailable: !!this.context,
            employeeId: this.context?.IdEmpleado || null
        };
    }
}

// Initialize content script
const worktimeContent = new WorktimeContentScript();

// Make it available globally for debugging
window.worktimeContent = worktimeContent;
