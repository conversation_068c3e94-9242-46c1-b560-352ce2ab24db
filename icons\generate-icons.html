<!DOCTYPE html>
<html>
<head>
    <title>Icon Generator</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .icon-preview { margin: 10px; display: inline-block; text-align: center; }
        canvas { border: 1px solid #ccc; margin: 5px; }
        button { padding: 10px 20px; margin: 5px; background: #E20074; color: white; border: none; border-radius: 4px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>T-Worktime Assistant Icon Generator</h1>
    <p>This page generates PNG icons from the SVG. Right-click on each canvas and "Save image as..." to save the PNG files.</p>
    
    <div id="icons"></div>
    <button onclick="generateIcons()">Generate Icons</button>

    <script>
        const svgContent = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 128 128" width="128" height="128">
            <circle cx="64" cy="64" r="60" fill="#E20074" stroke="#fff" stroke-width="4"/>
            <circle cx="64" cy="64" r="40" fill="#fff" stroke="#E20074" stroke-width="2"/>
            <line x1="64" y1="64" x2="64" y2="35" stroke="#E20074" stroke-width="3" stroke-linecap="round"/>
            <line x1="64" y1="64" x2="85" y2="64" stroke="#E20074" stroke-width="2" stroke-linecap="round"/>
            <circle cx="64" cy="64" r="3" fill="#E20074"/>
            <circle cx="64" cy="30" r="2" fill="#E20074"/>
            <circle cx="64" cy="98" r="2" fill="#E20074"/>
            <circle cx="98" cy="64" r="2" fill="#E20074"/>
            <circle cx="30" cy="64" r="2" fill="#E20074"/>
            <text x="64" y="110" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#E20074" font-weight="bold">T</text>
        </svg>`;

        function generateIcons() {
            const sizes = [16, 32, 48, 128];
            const container = document.getElementById('icons');
            container.innerHTML = '';

            sizes.forEach(size => {
                const div = document.createElement('div');
                div.className = 'icon-preview';
                
                const canvas = document.createElement('canvas');
                canvas.width = size;
                canvas.height = size;
                
                const ctx = canvas.getContext('2d');
                const img = new Image();
                
                img.onload = function() {
                    ctx.drawImage(img, 0, 0, size, size);
                };
                
                const blob = new Blob([svgContent], { type: 'image/svg+xml' });
                const url = URL.createObjectURL(blob);
                img.src = url;
                
                const label = document.createElement('div');
                label.textContent = `icon${size}.png (${size}x${size})`;
                
                div.appendChild(label);
                div.appendChild(canvas);
                container.appendChild(div);
            });
        }

        // Generate icons on page load
        window.onload = generateIcons;
    </script>
</body>
</html>
