/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 12px;
    line-height: 1.3;
    color: #333;
    background: #f5f5f5;
}

.container {
    width: 600px;
    min-height: 400px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* Header */
.header {
    background: #E20074;
    color: white;
    padding: 12px;
    text-align: center;
    position: relative;
}

.header h1 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 2px;
}

.status {
    font-size: 11px;
    opacity: 0.9;
}

/* Configuration Menu */
.config-menu {
    position: absolute;
    top: 12px;
    right: 12px;
}

.config-button {
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    padding: 4px 8px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 14px;
}

.config-button:hover {
    background: rgba(255,255,255,0.2);
}

.config-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    padding: 8px;
    min-width: 120px;
    z-index: 1000;
}

.config-item {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-bottom: 6px;
}

.config-item:last-child {
    margin-bottom: 0;
}

.config-item label {
    font-size: 10px;
    color: #333;
    white-space: nowrap;
}

.font-size-select {
    font-size: 10px;
    padding: 2px 4px;
    border: 1px solid #ddd;
    border-radius: 3px;
    background: white;
    color: #333;
}

/* Main content */
.main {
    padding: 12px;
}

section {
    margin-bottom: 12px;
}

section:last-child {
    margin-bottom: 0;
}

/* Week selector */
.week-selector {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.week-selector label {
    font-weight: 500;
    min-width: 80px;
}

.week-input {
    flex: 1;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

/* Employee info */
.employee-info {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    border-left: 4px solid #E20074;
}

.info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.info-item:last-child {
    margin-bottom: 0;
}

.label {
    font-weight: 500;
}

/* Time configuration */
.time-config h3 {
    margin-bottom: 12px;
    color: #E20074;
    font-size: 16px;
}

.default-times {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 16px;
}

.time-group {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    flex-wrap: wrap;
}

.time-group:last-child {
    margin-bottom: 12px;
}

.time-group label {
    font-weight: 500;
    min-width: 80px;
}

.time-input {
    padding: 2px 4px;
    border: 1px solid #ddd;
    border-radius: 0;
    font-size: 11px;
    width: 80px;
}

/* Apply control */
.apply-control {
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid #e0e0e0;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 11px;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    margin: 0;
}

/* Daily schedule */
.daily-schedule {
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    overflow: hidden;
}

.day-row {
    display: flex;
    align-items: center;
    padding: 8px;
    border-bottom: 1px solid #e0e0e0;
    background: white;
}

.day-row:last-child {
    border-bottom: none;
}

.day-row.absent {
    background: #fff3cd;
    color: #856404;
}

.day-name {
    font-weight: 500;
    min-width: 90px;
    font-size: 11px;
}

.day-times {
    display: flex;
    align-items: center;
    gap: 4px;
    flex: 1;
    flex-wrap: wrap;
}

.day-times .time-input {
    width: 50px;
    font-size: 11px;
    padding: 2px 4px;
}

.day-times span {
    font-size: 11px;
    color: #666;
}

/* Buttons */
.btn {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}

.btn:hover {
    opacity: 0.9;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background: #E20074;
    color: white;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-success {
    background: #28a745;
    color: white;
    width: 100%;
    padding: 12px;
    font-size: 14px;
}

.btn-warning {
    background: #ffc107;
    color: #212529;
    width: 100%;
    margin-top: 8px;
}

/* Progress */
.progress-bar {
    width: 100%;
    height: 8px;
    background: #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    background: #E20074;
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    text-align: center;
    font-size: 13px;
    color: #666;
}

/* Results */
.results h3 {
    margin-bottom: 12px;
    color: #E20074;
}

.result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    margin-bottom: 4px;
    border-radius: 4px;
    font-size: 13px;
}

.result-item.success {
    background: #d4edda;
    color: #155724;
}

.result-item.error {
    background: #f8d7da;
    color: #721c24;
}

.result-status {
    font-weight: 500;
}

/* Footer */
.footer {
    padding: 8px 12px;
    background: #f8f9fa;
    border-top: 1px solid #e0e0e0;
}

.help-text {
    font-size: 10px;
    color: #666;
    text-align: center;
}

/* Utility classes */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

.mb-2 {
    margin-bottom: 8px;
}
