# T-Worktime Assistant Browser Extension

A Manifest V3 browser extension for automated T-Worktime entries with calendar selection and time management.

## Features

- **Week Selection**: Choose any week using a calendar picker
- **Automatic Employee Detection**: Extracts employee ID from T-Worktime context
- **Absent Days Detection**: Identifies non-working days from the calendar
- **Batch Time Entry**: Submit work and break times for an entire week
- **Default Time Configuration**: Set default work hours (09:00-18:00) and break times (13:00-14:00)
- **Progress Tracking**: Real-time feedback during submission
- **Error Handling**: Clear error messages and validation

## Installation

### Development Installation

1. **Clone or download** this repository to your local machine
2. **Generate Icons** (optional but recommended):
   - Open `icons/generate-icons.html` in a web browser
   - Right-click each canvas and save as PNG with the correct filename
   - Or use any 16x16, 32x32, 48x48, and 128x128 PNG files as placeholders
3. **Load Extension in Chrome/Edge**:
   - Open Chrome/Edge and navigate to `chrome://extensions/` or `edge://extensions/`
   - Enable "Developer mode" (toggle in top-right corner)
   - Click "Load unpacked" and select the project folder
   - The extension should appear in your extensions list

### Production Installation

*Note: This extension is not yet published to the Chrome Web Store*

## Usage

### Prerequisites

- You must be logged into the T-Worktime system
- Navigate to the T-Worktime calendar page: `https://t-worktime.t-systems.es/TwTime/Calendario/CalendarioEmpleado`

### Steps

1. **Open Extension**: Click the T-Worktime Assistant icon in your browser toolbar
2. **Select Week**: Choose the week you want to configure using the week picker
3. **Load Data**: Click "Load Week" to extract your employee ID and detect absent days
4. **Configure Times**: 
   - Set default work hours and break times
   - Click "Apply to All Days" to use defaults for all working days
   - Or manually adjust times for individual days
5. **Submit**: Click "Submit All Entries" to send all time entries to T-Worktime
6. **Monitor Progress**: Watch the progress bar and review results

### Default Times

- **Work Hours**: 09:00 - 18:00
- **Break Time**: 13:00 - 14:00

These can be customized and will be saved for future use.

## Technical Details

### Architecture

- **Manifest V3**: Modern extension format with service worker
- **Content Script**: Extracts employee data from T-Worktime pages
- **Background Service**: Handles API calls and data processing
- **Popup Interface**: User-friendly interface for configuration

### Permissions

- `activeTab`: Access to the current T-Worktime tab
- `storage`: Save user preferences and default times
- `scripting`: Inject scripts to interact with T-Worktime
- Host permission for `https://t-worktime.t-systems.es/*`

### Files Structure

```
/
├── manifest.json          # Extension manifest
├── popup.html            # Main UI
├── popup.css             # Styling
├── popup.js              # Popup logic
├── content.js            # T-Worktime interaction
├── background.js         # Service worker
├── icons/                # Extension icons
│   ├── icon.svg          # Source SVG icon
│   ├── generate-icons.html # Icon generator
│   └── README.md         # Icon instructions
├── lib/                  # Utility functions
│   └── worktime-api.js   # T-Worktime API integration
├── script.js             # Original API function
├── script.js.bak         # Backup of original script
├── worktime.html         # Example T-Worktime page
├── instructions.md       # Original requirements
├── plan.md              # Development plan
└── README.md            # This file
```

## Development

### Testing

1. Load the extension in developer mode
2. Navigate to T-Worktime calendar page
3. Open the extension popup
4. Test each feature step by step
5. Check browser console for any errors

### Debugging

- **Extension Console**: Right-click extension icon → "Inspect popup"
- **Content Script**: Open browser DevTools on T-Worktime page
- **Background Script**: Go to `chrome://extensions/` → Extension details → "Inspect views: service worker"

## Troubleshooting

### Common Issues

1. **"Context not found"**: Make sure you're on the T-Worktime calendar page and logged in
2. **"Employee ID not found"**: Refresh the T-Worktime page and try again
3. **API errors**: Check your internet connection and T-Worktime login status
4. **Extension not loading**: Verify all files are present and manifest.json is valid

### Error Messages

- **"Please navigate to T-Worktime website first"**: Open the T-Worktime calendar page
- **"Could not extract employee data"**: Refresh the page and ensure you're logged in
- **"No entries to submit"**: Configure at least one day's times before submitting

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is for internal use with T-Systems T-Worktime system.

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review browser console for error messages
3. Ensure you're using the latest version of Chrome/Edge
4. Verify T-Worktime system is accessible and you're logged in
