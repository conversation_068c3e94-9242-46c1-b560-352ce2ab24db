<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>T-Worktime Assistant</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>T-Worktime Assistant</h1>
            <div class="status" id="status">Ready</div>
            <div class="font-size-control">
                <label for="fontSize">Font:</label>
                <select id="fontSize" class="font-size-select">
                    <option value="10">10px</option>
                    <option value="11">11px</option>
                    <option value="12" selected>12px</option>
                    <option value="13">13px</option>
                    <option value="14">14px</option>
                </select>
            </div>
        </header>

        <main class="main">
            <!-- Week Selection -->
            <section class="week-selector">
                <label for="weekPicker">Select Week:</label>
                <input type="week" id="weekPicker" class="week-input">
                <button id="loadWeek" class="btn btn-primary">Load Week</button>
            </section>

            <!-- Employee Info -->
            <section class="employee-info" id="employeeInfo" style="display: none;">
                <div class="info-item">
                    <span class="label">Employee ID:</span>
                    <span id="employeeId">-</span>
                </div>
                <div class="info-item">
                    <span class="label">Absent Days:</span>
                    <span id="absentDays">-</span>
                </div>
            </section>

            <!-- Time Configuration -->
            <section class="time-config" id="timeConfig" style="display: none;">
                <h3>Default Times</h3>
                <div class="default-times">
                    <div class="time-group">
                        <label>Work Hours:</label>
                        <input type="time" id="defaultClockIn" value="09:00" class="time-input" step="300">
                        <span>to</span>
                        <input type="time" id="defaultClockOut" value="18:00" class="time-input" step="300">
                    </div>
                    <div class="time-group">
                        <label>Break Time:</label>
                        <input type="time" id="defaultBreakStart" value="13:00" class="time-input" step="300">
                        <span>to</span>
                        <input type="time" id="defaultBreakEnd" value="14:00" class="time-input" step="300">
                    </div>
                    <button id="applyDefaults" class="btn btn-secondary">Apply to All Days</button>
                </div>

                <!-- Daily Schedule -->
                <div class="daily-schedule" id="dailySchedule">
                    <!-- Days will be populated by JavaScript -->
                </div>
            </section>

            <!-- Actions -->
            <section class="actions" id="actions" style="display: none;">
                <button id="submitEntries" class="btn btn-success">Submit All Entries</button>
                <button id="clearAll" class="btn btn-warning">Clear All</button>
            </section>

            <!-- Progress -->
            <section class="progress" id="progress" style="display: none;">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-text" id="progressText">Processing...</div>
            </section>

            <!-- Results -->
            <section class="results" id="results" style="display: none;">
                <h3>Results</h3>
                <div class="results-list" id="resultsList">
                    <!-- Results will be populated by JavaScript -->
                </div>
            </section>
        </main>

        <footer class="footer">
            <div class="help-text">
                Make sure you're logged into T-Worktime before using this extension.
            </div>
        </footer>
    </div>

    <script src="popup.js"></script>
</body>
</html>
