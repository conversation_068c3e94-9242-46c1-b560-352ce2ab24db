# T-Worktime Browser Extension Development Plan

## Project Overview
Build a Manifest V3 browser extension for making entries to T-Worktime system with calendar selection and automated time entry.

## Requirements
- **Manifest V3** compliance
- **Calendar** to select week
- **Time inputs** for clock in/out (default: 09:00-18:00)
- **Break times** for each day (default: 13:00-14:00)
- **Integration** with T-Worktime API
- **Employee ID detection** from T-Worktime context
- **Absent days detection** from Agenda data

## Development Phases

### Phase 1: Project Structure & Manifest
- [x] ✅ Create plan.md (this file)
- [x] ✅ Create manifest.json with Manifest V3 configuration
- [x] ✅ Set up extension directory structure
- [x] ✅ Define required permissions and content scripts

### Phase 2: Core Extension Files
- [x] ✅ Create popup.html - Main UI interface
- [x] ✅ Create popup.css - Extension styling
- [x] ✅ Create popup.js - Popup logic and controls
- [x] ✅ Create content.js - T-Worktime website interaction
- [x] ✅ Create background.js - Service worker for background tasks

### Phase 3: UI Components
- [x] ✅ Implement week calendar selector
- [x] ✅ Create time input fields for each day:
  - [x] ✅ Clock in time (default: 09:00)
  - [x] ✅ Clock out time (default: 18:00)
  - [x] ✅ Break start time (default: 13:00)
  - [x] ✅ Break end time (default: 14:00)
- [x] ✅ Add submit button for batch entry
- [x] ✅ Add status/feedback display

### Phase 4: Core Functionality
- [x] ✅ Integrate existing sendWorktimeEntry function from script.js
- [x] ✅ Implement employee ID extraction:
  - [x] ✅ Get https://t-worktime.t-systems.es/TwTime/Calendario/CalendarioEmpleado
  - [x] ✅ Parse 'context' variable from response
  - [x] ✅ Extract context.IdEmpleado
- [x] ✅ Implement absent days detection:
  - [x] ✅ Parse Agenda array from context
  - [x] ✅ Identify dates where Agenda[*].Trabajable == false
  - [x] ✅ Store and display absent days
- [x] ✅ Add batch time entry submission
- [x] ✅ Implement error handling and validation

### Phase 5: Integration & Testing
- [x] ✅ Test extension loading in browser
- [x] ✅ Verify permissions and content script injection
- [x] ✅ Test T-Worktime website detection and data extraction
- [x] ✅ Test time entry submission functionality (FIXED: Now creates 3 entries per day)
- [x] ✅ Validate error handling and user feedback
- [ ] Test with different week selections
- [x] ✅ Verify absent days detection accuracy

### Phase 6: Polish & Documentation
- [ ] Add loading states and progress indicators
- [ ] Improve error messages and user guidance
- [ ] Add keyboard shortcuts and accessibility
- [ ] Create user documentation
- [ ] Add extension icons and branding

## Technical Architecture

### Files Structure
```
/
├── manifest.json          # Extension manifest
├── popup.html            # Main UI
├── popup.css             # Styling
├── popup.js              # Popup logic
├── content.js            # T-Worktime interaction
├── background.js         # Service worker
├── icons/                # Extension icons
└── lib/                  # Utility functions
    └── worktime-api.js   # T-Worktime API integration
```

### Key Components
1. **Popup Interface** - Week selector, time inputs, submit controls
2. **Content Script** - Extracts employee data and context from T-Worktime
3. **Background Service** - Handles API calls and data persistence
4. **API Integration** - Reuses existing sendWorktimeEntry function

### Data Flow
1. User selects week in popup
2. Content script extracts employee ID and absent days
3. User configures times for each day
4. Background service submits entries via T-Worktime API
5. UI shows progress and results

## Success Criteria
- [ ] Extension loads successfully in Chrome/Edge
- [ ] Can detect and extract employee ID from T-Worktime
- [ ] Can identify absent days correctly
- [ ] Can submit time entries for a full week
- [ ] Provides clear feedback on success/failure
- [ ] Handles errors gracefully
