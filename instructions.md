
lets build a browser extension for making entries to T-Worktime
- use manifest v3
- make plan how to proceed
    - create plan.md
    - check successfull done points


inputs:
 *  Calender to select week
 *  Clock in / out times for each day (if not provided, use 09:00 and 18:00)
 *  Break start and end times for each day (if not provided, use 13:00 and 14:00)
 
/*
- get https://t-worktime.t-systems.es/TwTime/Calendario/CalendarioEmpleado (example response: see worktime.html)
- get json from 'context' var
- get context.IdEmpleado as IdEmpleado
- store any dates from today which are Agenda[*].Trabajable == false as absent days
- list results to see if its working

*/