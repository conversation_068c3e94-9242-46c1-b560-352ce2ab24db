/**
 * T-Worktime Assistant <PERSON><PERSON>
 */

class WorktimePopup {
    constructor() {
        this.employeeId = null;
        this.absentDays = [];
        this.currentWeek = null;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setCurrentWeek();
        this.loadStoredData();
    }

    setupEventListeners() {
        // Week selection
        document.getElementById('loadWeek').addEventListener('click', () => this.loadWeekData());
        
        // Default time application
        document.getElementById('applyDefaults').addEventListener('click', () => this.applyDefaultTimes());
        
        // Submit entries
        document.getElementById('submitEntries').addEventListener('click', () => this.submitAllEntries());
        
        // Clear all
        document.getElementById('clearAll').addEventListener('click', () => this.clearAllData());
        
        // Week picker change
        document.getElementById('weekPicker').addEventListener('change', (e) => {
            this.currentWeek = e.target.value;
        });

        // Font size change
        document.getElementById('fontSize').addEventListener('change', (e) => {
            this.changeFontSize(e.target.value);
        });
    }

    setCurrentWeek() {
        const now = new Date();
        const year = now.getFullYear();
        const week = this.getWeekNumber(now);
        const weekString = `${year}-W${week.toString().padStart(2, '0')}`;
        
        document.getElementById('weekPicker').value = weekString;
        this.currentWeek = weekString;
    }

    getWeekNumber(date) {
        const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
        const dayNum = d.getUTCDay() || 7;
        d.setUTCDate(d.getUTCDate() + 4 - dayNum);
        const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
        return Math.ceil((((d - yearStart) / 86400000) + 1) / 7);
    }

    async loadWeekData() {
        this.updateStatus('Loading employee data...');

        try {
            // Check if we're on T-Worktime page
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab.url.includes('t-worktime.t-systems.es')) {
                this.showError('Please navigate to T-Worktime website first');
                return;
            }

            // Use the content script to get employee data
            const results = await chrome.scripting.executeScript({
                target: { tabId: tab.id },
                function: () => {
                    // Check if content script is available
                    if (window.worktimeContent) {
                        return window.worktimeContent.getEmployeeData();
                    } else {
                        // Fallback: try to extract data directly
                        try {
                            if (window.context && window.context.IdEmpleado) {
                                const absentDays = [];
                                const today = new Date();

                                if (window.context.Agenda && Array.isArray(window.context.Agenda)) {
                                    window.context.Agenda.forEach(day => {
                                        if (day.Trabajable === false) {
                                            const dayDate = new Date(day.Fecha);
                                            if (dayDate >= today) {
                                                absentDays.push(day.Fecha.split('T')[0]);
                                            }
                                        }
                                    });
                                }

                                return {
                                    success: true,
                                    employeeId: window.context.IdEmpleado,
                                    absentDays: absentDays,
                                    contextAvailable: true
                                };
                            } else {
                                return {
                                    success: false,
                                    error: 'Context not available',
                                    employeeId: null,
                                    absentDays: [],
                                    contextAvailable: false
                                };
                            }
                        } catch (error) {
                            return {
                                success: false,
                                error: error.message,
                                employeeId: null,
                                absentDays: [],
                                contextAvailable: false
                            };
                        }
                    }
                }
            });

            if (results && results[0] && results[0].result) {
                const data = results[0].result;

                if (data.success) {
                    this.employeeId = data.employeeId;
                    this.absentDays = data.absentDays;

                    this.displayEmployeeInfo();
                    this.generateDailySchedule();
                    this.showSections(['employeeInfo', 'timeConfig', 'actions']);
                    this.updateStatus('Ready to configure times');
                } else {
                    this.showError(data.error || 'Could not extract employee data. Make sure you are logged in.');
                }
            } else {
                this.showError('Could not extract employee data. Make sure you are logged in.');
            }
        } catch (error) {
            console.error('Error loading week data:', error);
            this.showError('Error loading data: ' + error.message);
        }
    }

    displayEmployeeInfo() {
        document.getElementById('employeeId').textContent = this.employeeId || 'Not found';
        document.getElementById('absentDays').textContent = 
            this.absentDays.length > 0 ? this.absentDays.join(', ') : 'None';
    }

    generateDailySchedule() {
        const scheduleContainer = document.getElementById('dailySchedule');
        scheduleContainer.innerHTML = '';

        const weekDates = this.getWeekDates(this.currentWeek);

        weekDates.forEach((date, index) => {
            // Get the actual day name from the date
            const dayName = date.toLocaleDateString('en-US', { weekday: 'long' });
            const dateStr = date.toISOString().split('T')[0];
            const isAbsent = this.absentDays.includes(dateStr);
            const isWeekend = index >= 5; // Saturday and Sunday (since we start from Monday)

            // Skip weekends
            if (isWeekend) {
                return;
            }

            const dayRow = document.createElement('div');
            dayRow.className = `day-row ${isAbsent ? 'absent' : ''}`;
            
            dayRow.innerHTML = `
                <div class="day-name">${dayName}<br><small>${dateStr}</small></div>
                <div class="day-times">
                    ${isAbsent ? '<span>Absent Day</span>' :
                      `<input type="time" class="time-input clock-in" value="09:00" data-day="${dateStr}" step="300">
                       <span>to</span>
                       <input type="time" class="time-input clock-out" value="18:00" data-day="${dateStr}" step="300">
                       <span>|</span>
                       <input type="time" class="time-input break-start" value="13:00" data-day="${dateStr}" step="300">
                       <span>to</span>
                       <input type="time" class="time-input break-end" value="14:00" data-day="${dateStr}" step="300">`
                    }
                </div>
            `;

            scheduleContainer.appendChild(dayRow);
        });
    }

    getWeekDates(weekString) {
        const [year, week] = weekString.split('-W').map(Number);

        // Calculate the Monday of the specified ISO week
        // ISO week 1 is the first week with at least 4 days in the new year
        const jan4 = new Date(year, 0, 4); // January 4th is always in week 1
        const jan4Day = jan4.getDay() || 7; // Convert Sunday (0) to 7
        const mondayOfWeek1 = new Date(jan4);
        mondayOfWeek1.setDate(jan4.getDate() - jan4Day + 1); // Go back to Monday

        // Calculate the Monday of the target week
        const mondayOfTargetWeek = new Date(mondayOfWeek1);
        mondayOfTargetWeek.setDate(mondayOfWeek1.getDate() + (week - 1) * 7);

        // Generate all 7 days starting from Monday
        const dates = [];
        for (let i = 0; i < 7; i++) {
            const date = new Date(mondayOfTargetWeek);
            date.setDate(mondayOfTargetWeek.getDate() + i);
            dates.push(date);
        }

        return dates;
    }

    applyDefaultTimes() {
        const defaultClockIn = document.getElementById('defaultClockIn').value;
        const defaultClockOut = document.getElementById('defaultClockOut').value;
        const defaultBreakStart = document.getElementById('defaultBreakStart').value;
        const defaultBreakEnd = document.getElementById('defaultBreakEnd').value;

        document.querySelectorAll('.clock-in').forEach(input => input.value = defaultClockIn);
        document.querySelectorAll('.clock-out').forEach(input => input.value = defaultClockOut);
        document.querySelectorAll('.break-start').forEach(input => input.value = defaultBreakStart);
        document.querySelectorAll('.break-end').forEach(input => input.value = defaultBreakEnd);

        this.updateStatus('Default times applied to all working days');
    }

    async submitAllEntries() {
        const entries = this.collectTimeEntries();
        
        if (entries.length === 0) {
            this.showError('No entries to submit');
            return;
        }

        this.showProgress(true);
        this.updateProgress(0, 'Starting submission...');

        const results = [];
        const total = entries.length;

        for (let i = 0; i < entries.length; i++) {
            const entry = entries[i];
            const progress = ((i + 1) / total) * 100;
            
            this.updateProgress(progress, `Submitting ${entry.type} (${entry.startTime}-${entry.endTime}) for ${entry.date}...`);

            try {
                // Send message to background script to handle the API call
                const result = await chrome.runtime.sendMessage({
                    action: 'submitEntry',
                    entry: entry,
                    employeeId: this.employeeId
                });

                results.push({
                    date: entry.date,
                    type: entry.type,
                    timeRange: `${entry.startTime}-${entry.endTime}`,
                    success: result.success,
                    message: result.message
                });

                // Small delay between requests
                await new Promise(resolve => setTimeout(resolve, 500));
            } catch (error) {
                results.push({
                    date: entry.date,
                    type: entry.type,
                    timeRange: `${entry.startTime}-${entry.endTime}`,
                    success: false,
                    message: error.message
                });
            }
        }

        this.showProgress(false);
        this.displayResults(results);
        this.updateStatus('Submission completed');
    }

    collectTimeEntries() {
        const entries = [];
        const dayRows = document.querySelectorAll('.day-row:not(.absent)');

        dayRows.forEach(row => {
            const clockIn = row.querySelector('.clock-in');
            const clockOut = row.querySelector('.clock-out');
            const breakStart = row.querySelector('.break-start');
            const breakEnd = row.querySelector('.break-end');

            if (clockIn && clockOut && breakStart && breakEnd) {
                const date = clockIn.dataset.day;

                // Create 3 entries per day:
                // 1. Morning worktime (clock-in to break-start)
                entries.push({
                    date: date,
                    startTime: clockIn.value,
                    endTime: breakStart.value,
                    type: 'worktime'
                });

                // 2. Break time (break-start to break-end)
                entries.push({
                    date: date,
                    startTime: breakStart.value,
                    endTime: breakEnd.value,
                    type: 'break'
                });

                // 3. Afternoon worktime (break-end to clock-out)
                entries.push({
                    date: date,
                    startTime: breakEnd.value,
                    endTime: clockOut.value,
                    type: 'worktime'
                });
            }
        });

        return entries;
    }

    displayResults(results) {
        const resultsContainer = document.getElementById('resultsList');
        resultsContainer.innerHTML = '';

        results.forEach(result => {
            const resultItem = document.createElement('div');
            resultItem.className = `result-item ${result.success ? 'success' : 'error'}`;
            
            resultItem.innerHTML = `
                <span>${result.date} - ${result.type} (${result.timeRange || 'N/A'})</span>
                <span class="result-status">${result.success ? 'Success' : 'Failed'}</span>
            `;

            if (!result.success) {
                resultItem.title = result.message;
            }

            resultsContainer.appendChild(resultItem);
        });

        this.showSections(['results']);
    }

    showProgress(show) {
        document.getElementById('progress').style.display = show ? 'block' : 'none';
    }

    updateProgress(percent, text) {
        document.getElementById('progressFill').style.width = percent + '%';
        document.getElementById('progressText').textContent = text;
    }

    showSections(sectionIds) {
        const allSections = ['employeeInfo', 'timeConfig', 'actions', 'progress', 'results'];
        
        allSections.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.style.display = sectionIds.includes(id) ? 'block' : 'none';
            }
        });
    }

    updateStatus(message) {
        document.getElementById('status').textContent = message;
    }

    showError(message) {
        this.updateStatus('Error: ' + message);
        console.error(message);
    }

    clearAllData() {
        if (confirm('Are you sure you want to clear all time entries?')) {
            document.querySelectorAll('.time-input').forEach(input => {
                if (input.classList.contains('clock-in')) input.value = '09:00';
                else if (input.classList.contains('clock-out')) input.value = '18:00';
                else if (input.classList.contains('break-start')) input.value = '13:00';
                else if (input.classList.contains('break-end')) input.value = '14:00';
            });
            
            this.updateStatus('All entries cleared');
        }
    }

    loadStoredData() {
        // Load any previously stored configuration
        chrome.storage.local.get(['defaultTimes', 'fontSize'], (result) => {
            if (result.defaultTimes) {
                document.getElementById('defaultClockIn').value = result.defaultTimes.clockIn || '09:00';
                document.getElementById('defaultClockOut').value = result.defaultTimes.clockOut || '18:00';
                document.getElementById('defaultBreakStart').value = result.defaultTimes.breakStart || '13:00';
                document.getElementById('defaultBreakEnd').value = result.defaultTimes.breakEnd || '14:00';
            }
            if (result.fontSize) {
                document.getElementById('fontSize').value = result.fontSize;
                this.changeFontSize(result.fontSize);
            }
        });
    }

    changeFontSize(size) {
        document.body.style.fontSize = size + 'px';
        // Save the preference
        chrome.storage.local.set({ fontSize: size });
    }

    saveDefaultTimes() {
        const defaultTimes = {
            clockIn: document.getElementById('defaultClockIn').value,
            clockOut: document.getElementById('defaultClockOut').value,
            breakStart: document.getElementById('defaultBreakStart').value,
            breakEnd: document.getElementById('defaultBreakEnd').value
        };

        chrome.storage.local.set({ defaultTimes });
    }
}

// Initialize the popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new WorktimePopup();
});
