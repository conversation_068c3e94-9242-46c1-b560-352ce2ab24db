
<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="description" content="">
    <meta name="author" content="">

    <title>T-WorkTime</title>
    <link href="/images/favicon.ico" rel="shortcut icon" type="image/x-icon" />


    <link rel="stylesheet" href="/css/vendor.css?v=qgrGjMfasHGVnjKPTCMt66zQKmzDI68ugboE6Fhcqmo" />
    <link rel="stylesheet" href="/css/Site.css?v=sffNk-Mz8CovUNcE43IpNz-nGqHGa8Xr2sVf_X4nxiI" />
    <link rel="stylesheet" href="/css/SiteNet.css?638845464106695652" />
    
    
    <script src="/js/vendor.js?v=tg6rCdDc23hKxVmP5wRyVVFhIB7rvZ7CCcdeNrskytY"></script>
   

    <script src="/js/foolproof/jquery.validate.min.js" type="text/javascript"></script>
    <script src="/js/foolproof/jquery.validate.unobtrusive.min.js" type="text/javascript"></script>
    <script src="/js/foolproof/jquery.validate.globalize.min.js" type="text/javascript"></script>
    <script src="/js/foolproof/jquery.unobtrusive-ajax.min.js" type="text/javascript"></script>

    <script src="/js/foolproof/MvcFoolproof.core.js"></script>
    <script src="/js/foolproof/MvcFoolproof.JQuery.Validation.js"></script>
    <script src="/js/foolproof/mvcfoolproof.unobtrusive.js"></script>

    <script src="/js/diccionarioPortal.js?v=TTRmd-EAk3ioiIkglMoEMKKBWZluzB5eC7_LRdpfcJI"></script>
    <script src="/js/portalCore.js?v=OrIxgjich7wRelMwotH5dWbxWfzwUdF2WZ5Fpyab94I"></script>
    <script src="/Account/CldrData?t=5eN6cnCZ3Yg%3D"></script>

        <script src="/js/dx.messages.es.js" type="text/javascript"></script>

    
    

    
    
</head>

<body class="dx-viewport">
    <div id="loadPanelPortal"></div><script>DevExpress.utils.readyCallbacks.add((function($){$("#loadPanelPortal").dxLoadPanel({"message":"Procesando, por favor espere...","shadingColor":"rgba(226,0,116,0.4)","visible":false,"showIndicator":true,"showPane":true,"shading":true,"closeOnOutsideClick":false});}).bind(this, jQuery));</script>
    <div id="ToolTipDatosControl"></div><script>DevExpress.utils.readyCallbacks.add((function($){$("#ToolTipDatosControl").dxTooltip({"target":"","closeOnOutsideClick":true,"position":"left"});}).bind(this, jQuery));</script>
    <div id="toastPortalTSContainer"></div>
    <div id="app-side-nav-outer-toolbar">
        <div id="HeaderApp" class="layout-header" style="background-color:#E20074;">
            <div id="toolBarMenuPrincipal"></div><script>DevExpress.utils.readyCallbacks.add((function($){$("#toolBarMenuPrincipal").dxToolbar({"items":[{"location":"before","cssClass":"header-logo-tsys"},{"options":{"icon":"close","onClick":PortalAspNetCoreApp1.onMenuButtonClick,"onInitialized":MenuInicializado},"widget":"dxButton","location":"before","cssClass":"menu-button"},{"location":"before","cssClass":"header-space"},{"location":"before","cssClass":"icon-title"},{"html":"\u003cdiv\u003eT-WorkTime\u003c/div\u003e","location":"before","cssClass":"header-title"},{"options":{"icon":"/images/icono_soporte_telefonico.png","onClick":soporteBtnClick,"hint":"Reporta incidencias y realiza consultas"},"widget":"dxButton","location":"after","cssClass":"menu-button"},{"options":{"icon":"user","onClick":VisualizarDatosUsuario},"widget":"dxButton","location":"after","cssClass":"menu-button"},{"options":{"icon":"help","onClick":helpBtnClick},"widget":"dxButton","location":"after","cssClass":"menu-button"},{"location":"after","cssClass":"header-space"}]});}).bind(this, jQuery));</script>
        </div>
        <div class="layout-body layout-body-hidden">
<div id="DatosCabeceraUsuario"></div><script>DevExpress.utils.readyCallbacks.add((function($){$("#DatosCabeceraUsuario").dxPopup({"position":"right top","width":350.0,"height":400.0,"showTitle":true,"title":"Datos de usuario","visible":false,"dragEnabled":false,"closeOnOutsideClick":true,"shading":false,"showCloseButton":false,"toolbarItems":[{"toolbar":"bottom","location":"before","options":{"icon":"refresh","text":"Recargar","hint":"Recargar","onClick":RecargarApp},"widget":"dxButton"},{"toolbar":"bottom","location":"after","options":{"icon":"runner","text":"Salir","hint":"Salir","onClick":SalirApp},"widget":"dxButton"}]});}).bind(this, jQuery));</script><script type="text/html" id="ContenidoDatosUsuario">                    <table>
                        <tr>
                            <td style="vertical-align: top;">
                             <img id="fotoAvatar" src="/images/Avatares/default.png" width="86" height="86" class="avatar" onload="getAvatar()" />
                            </td>
                            <td>
                                <ul role="none" class="dx-menu-items-container" style="min-width: 210px; padding-left: 5px;">
                                    <li role="none" class="dx-menu-item-wrapper li-sin-punto">
                                        <div class="dx-item dx-menu-item" role="menuitem" tabindex="-1">
                                            <div class="dx-item-content dx-menu-item-content">
                                                <span style="color: rgba(0,0,0,.87);"><b>ALT, CHRISTIAN</b><hr style="border: 1px solid rgba(0, 0, 0, 0.1);"></span>
                                            </div>
                                        </div>
                                    </li>
                                    <li role="none" class="dx-menu-item-wrapper li-sin-punto">
                                        <div class="dx-item dx-menu-item" role="menuitem" tabindex="-1" style="margin-top: 20px;">
                                            <div class="dx-item-content dx-menu-item-content">

                                                <span style="color: rgba(0,0,0,.87);"><b>Datos de usuario</b></span>
                                            </div>
                                        </div>
                                    </li>
                                    <li role="none" class="dx-menu-item-wrapper li-sin-punto">
                                        <div class="dx-item dx-menu-item" role="menuitem" tabindex="-1" style="margin-top: 5px;">
                                            <div class="dx-item-content dx-menu-item-content">
                                                <span style="color: rgba(0,0,0,.87);">EMEA2: </span><span style="color: rgba(0,0,0,.87);">A672600</span>
                                            </div>
                                        </div>
                                    </li>
                                    <li role="none" class="dx-menu-item-wrapper li-sin-punto">
                                        <div class="dx-item dx-menu-item" role="menuitem" tabindex="-1" style="margin-top: 5px;">
                                            <div class="dx-item-content dx-menu-item-content">
                                                <span style="color: rgba(0,0,0,.87);">IdSap: </span><span style="color: rgba(0,0,0,.87);">58204933</span>
                                            </div>
                                        </div>
                                    </li>
                                    <li role="none" class="dx-menu-item-wrapper li-sin-punto">
                                        <div class="dx-item dx-menu-item" role="menuitem" tabindex="-1" style="margin-top: 5px;">
                                            <div class="dx-item-content dx-menu-item-content">
                                                <span style="color: rgba(0,0,0,.87);">Login: </span><span style="color: rgba(0,0,0,.87);">calt</span>
                                            </div>
                                        </div>
                                    </li>
                                    <li role="none" class="dx-menu-item-wrapper li-sin-punto">
                                        <div class="dx-item dx-menu-item" role="menuitem" tabindex="-1" style="margin-top: 5px;">
                                            <div class="dx-item-content dx-menu-item-content">
                                                <span style="color: rgba(0,0,0,.87);">CIAM: </span><span style="color: rgba(0,0,0,.87);">672600</span><hr style="border: 1px solid rgba(0, 0, 0, 0.1);">
                                            </div>
                                        </div>
                                    </li>
                                    <li role="none" class="dx-menu-item-wrapper li-sin-punto">
                                        <div class="dx-item dx-menu-item" role="menuitem" tabindex="-1" style="margin-top: 20px;">
                                            <div class="dx-item-content dx-menu-item-content">
                                                <span style="color: rgba(0,0,0,.87);"><b>Perfiles usuario</b></span>
                                            </div>
                                        </div>
                                    </li>
                                        <li role="none" class="dx-menu-item-wrapper li-sin-punto">
                                            <div class="dx-item dx-menu-item" role="menuitem" tabindex="-1" style="margin-top: 5px;">
                                                <div class="dx-item-content dx-menu-item-content">
                                                    <span style="color: rgba(0,0,0,.87);">Usuario</span>
                                                </div>
                                            </div>
                                        </li>
                                </ul>
                            </td>
                        </tr>
                    </table>
</script><script type="text/html" id="calendario-views1-appointmentTemplate">
                                            <% var registroEmp = getRegistroById(appointmentData.Id); %>
                                            <div class="dx-scheduler-appointment-title"><%- registroEmp.Color %> (<%- registroEmp.HoraInicio %> - <%- registroEmp.HoraFin %>)</div>   
                                          </script><script type="text/html" id="calendario-appointmentTooltipTemplate">
                                    <%
    var regHorario = getRegistroById(appointmentData.Id),
        data = arguments[0];
%>
<div class="dx-tooltip-appointment-item">
    <div class="dx-tooltip-appointment-item-marker">
        <div class="dx-tooltip-appointment-item-marker-body" style="background: <%- regHorario.CodigoColor %> none repeat scroll 0% 0%; "></div>
    </div>
    <div class="dx-tooltip-appointment-item-content">
        <div class="dx-tooltip-appointment-item-content-subject"><%- regHorario.Color %></div>
        <div class="dx-tooltip-appointment-item-content-date"><%- regHorario.HoraInicio %> - <%- regHorario.HoraFin %></div>
    </div>
    <%
    if(regHorario.IsDeletable && 0 != 1){ %>
    <%!function(){%><div id="<%=arguments[0]%>"></div><%DevExpress.aspnet.createComponent("dxButton",{"icon":"trash","hint":"Descartar periodo tarde","onClick":function(e){
            $("#calendario").dxScheduler("instance").deleteAppointment(appointmentData);
            $("#calendario").dxScheduler("instance").hideAppointmentTooltip();
            e.event.stopPropagation();
        }},arguments[0])%><%}("dx-" + new DevExpress.data.Guid())%>
    <% }
    %>
    <%
    if(regHorario.IsPartitionable && 0 != 1){ %>
    <%!function(){%><div id="<%=arguments[0]%>"></div><%DevExpress.aspnet.createComponent("dxButton",{"icon":"fields","hint":"Particionar periodo tarde","onClick":function(e){
            var oldData = ClonarObjeto(appointmentData);
            appointmentData.Particionar = true;
            $("#calendario").dxScheduler("instance").updateAppointment(oldData,appointmentData);
            $("#calendario").dxScheduler("instance").hideAppointmentTooltip();
            e.event.stopPropagation();
        }},arguments[0])%><%}("dx-" + new DevExpress.data.Guid())%>
    <% }
    %>
    <!--<div class="dx-tooltip-appointment-item-delete-button-container">
        <div class="dx-tooltip-appointment-item-delete-button dx-button dx-button-normal dx-button-mode-text dx-widget dx-button-has-icon" aria-label="trash" tabindex="0" role="button">
            <div class="dx-button-content"><i class="dx-icon dx-icon-trash"></i></div>
        </div>
    </div>-->
</div>
                                </script><script type="text/html" id="calendario-appointmentTemplate">
                <% var registroEmp = getRegistroById(appointmentData.Id); %>
                        <div class="dx-scheduler-appointment-title"><%- registroEmp.Color %></div>
                        <div class="dx-scheduler-appointment-content-details" style="white-space: inherit;">
                                <div class="dx-scheduler-appointment-content-date"><%- registroEmp.HoraInicio %> - <%- registroEmp.HoraFin %></div>
                        </div>
            </script>        <script>
                    function VisualizarDatosUsuario() {
                        var popup = $("#DatosCabeceraUsuario").dxPopup("instance");
                        popup.option("contentTemplate", $("#ContenidoDatosUsuario"));
                        popup.show();
                    }

                    function SalirApp() {
                        window.location.href = "/Account/LogOff";
                    }

                    function RecargarApp() {
                        EjecutarAjax(null, '/Cache/RecargarAplicacion', 'POST', null, PostRecargarAplicacion, 'loadPanelPortal', 'Recargadon aplicación...', null, null, true, PostRecargarAplicacionError, false);
                    }

                    function PostRecargarAplicacion() {
                        AlertaCliente(`Se ha recargado correctamente la aplicación.`, `Recarga aplicación`, recargar);
                    }

                    function PostRecargarAplicacionError() {
                        ShowToastError(`Se ha producido un error al recargar la aplicación.`);
                    }

                    function recargar(valor) {
                        top.location.reload();
                        //parent.loadingPanelDaapGeneral.SetText("Recargando página");
                        //parent.loadingPanelDaapGeneral.Show();
                    }

        </script>



            <div id="layout-drawer">
                    <div id="layout-drawer-scrollview" class="with-footer">
                        <div class="content" style="padding: 10px 10px 4% 10px;">
                            
<div id="buttonContainer" style="display:flex;flex-direction:row;">
<div id="VisorHoras" style="width:320px;" class="ceFormTextc">
    <div class="dx-fieldset" style="padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;">
        <div class="dx-field">
            <div class="dx-field-label" style="font-weight:bold;">Horas convenio</div>
            <div class="dx-field-value" style="float:right !important;">
<script>DevExpress.config({"serverDecimalSeparator":","});DevExpress.aspnet.setTemplateEngine();</script><div id="txtHorasConvenio"></div><script>DevExpress.utils.readyCallbacks.add((function($){$("#txtHorasConvenio").dxTextBox({"name":"HorasConvenio","value":"00:00","width":45.0,"readOnly":true,"visible":true,"inputAttr":{"id":"HorasConvenio"}});}).bind(this, jQuery));</script>            </div>

        </div>
    </div>
    <div class="dx-fieldset" style="padding: 0px 0px 0px 0px; margin: 0px 0px 0px 0px;">
        <div class="dx-field">

            <div class="dx-field-label" style="font-weight:bold;">Horas registradas</div>
            <div class="dx-field-value" style="float:right !important;">
<div id="txtHorasReales"></div><script>DevExpress.utils.readyCallbacks.add((function($){$("#txtHorasReales").dxTextBox({"name":"HorasReales","value":"00:00","width":45.0,"readOnly":true,"inputAttr":{"id":"HorasReales"}});}).bind(this, jQuery));</script>            </div>
        </div>
    </div>
</div>
        <h6 id="tituloEmpleado" style="margin-top: 15px; margin-left: -120px;">CHRISTIAN ALT </h6>
<div id="CERepliWeek" class="ceFormText">
<div id="ceExtConfDate"></div><script>DevExpress.utils.readyCallbacks.add((function($){$("#ceExtConfDate").dxDateBox({"name":"Monday","value":new Date(2025, 5, 2),"visible":false,"inputAttr":{"id":"Monday"}});}).bind(this, jQuery));</script><div id="devextreme3"></div><script>DevExpress.utils.readyCallbacks.add((function($){$("#devextreme3").dxButton({"text":"Replicar semana","useSubmitBehavior":false,"hint":"Replica la configuración de esta semana para el resto de semanas no validadas del año.","onClick":submitExtendConf,"visible":false,"type":"success"});}).bind(this, jQuery));</script></div>
<script>
    function submitExtendConf(s, e, e2) {
        $('#loadPanelPortal').dxLoadPanel('instance').show();
        value = $('#ceExtConfDate').dxDateBox('instance').option('text');
        // $.ajax({
        //     url: "https://t-worktime.t-systems.es/TWTime/Calendario/ReplicarSemana",
        //     method: "POST",
        //     data: {
        //         Monday: value//.toLocaleDateString()
        //     }
        // })
        // .done(function (s, e) {
        //     $('#loadPanelPortal').dxLoadPanel('instance').hide();
        // })
        // .fail(function (s,e) {
        //     $('#loadPanelPortal').dxLoadPanel('instance').hide();
        //     dataSourceLoadErrorGeneral(s.responseText);
        // });
    }
</script><div id="CEValWeek" class="ceFormText">
<div id="cevalweekdate"></div><script>DevExpress.utils.readyCallbacks.add((function($){$("#cevalweekdate").dxDateBox({"name":"Monday","value":new Date(2025, 5, 2),"visible":false,"inputAttr":{"id":"Monday"}});}).bind(this, jQuery));</script><div id="IdValidarSemana"></div><script>DevExpress.utils.readyCallbacks.add((function($){$("#IdValidarSemana").dxButton({"text":"Validar semana actual","useSubmitBehavior":false,"hint":"Valida los horarios registrados de la semana","visible":true,"type":"success","onClick":submitValidarSemana});}).bind(this, jQuery));</script>
</div>
<script>
    function submitValidarSemana() {
        $('#loadPanelPortal').dxLoadPanel('instance').show();
        value = $('#cevalweekdate').dxDateBox('instance').option('text');
        $.ajax({
            url: "https://t-worktime.t-systems.es/TWTime/Calendario/ValidarSemana",
            method: "POST",
            data: {
                Monday: value//.toLocaleDateString()
            }
        })
        .done(function (s, e) {

            $('#loadPanelPortal').dxLoadPanel('instance').hide();
            $('#IdValidarSemana').dxButton('instance').option("visible", false);
            $("#calendario").dxScheduler("instance").getDataSource().reload();
        })
        .fail(function (s, e) {
            $('#loadPanelPortal').dxLoadPanel('instance').hide();
            dataSourceLoadErrorGeneral(s.responseText);
        });
    }
</script><div id="regularizarSemana" class="ceFormText">
<div id="IdRegularizarSemanaBtn"></div><script>DevExpress.utils.readyCallbacks.add((function($){$("#IdRegularizarSemanaBtn").dxButton({"text":"Regularizar semana","useSubmitBehavior":false,"hint":"Regulariza la semana cuadrando las horas realizadas con las vacaciones, para posteriormente permitirle ajustar su jornada","visible":false,"type":"success","onClick":submitRegWeek});}).bind(this, jQuery));</script>
</div>
<script>
    function submitRegWeek()
    {
        var url = "https://t-worktime.t-systems.es/TWTime/Calendario/RegularizarSemana";
        //EjecutarAjax(null, url, "PUT", objPeriodo, postSubmitRegWeek, "loadPanelPortal", "Procesando la revisión de la semana", null, null, true, null, true);
        $.ajax({
            url: url,
            method: "PUT",
            data: {
                lunesSemana: lunesSemana//.toLocaleDateString()
            }
        })
        .done(function (s, e) {
            var schedulerInstance = $("#calendario").dxScheduler("instance");
            schedulerInstance.getDataSource().reload();
        })
        .fail(function (s, e) {
            $('#loadPanelPortal').dxLoadPanel('instance').hide();
            dataSourceLoadErrorGeneral(s.responseText);
        });
    }
</script>
<div id="Leyendas" class="ceFormText">
<div id="btnLeyenda"></div><script>DevExpress.utils.readyCallbacks.add((function($){$("#btnLeyenda").dxButton({"text":"Definiciones","useSubmitBehavior":false,"onClick":submitLeyendas,"visible":true,"type":"normal"});}).bind(this, jQuery));</script>    <div id="tpLeyenda"></div><script>DevExpress.utils.readyCallbacks.add((function($){$("#tpLeyenda").dxTooltip({"target":"#btnLeyenda","hideEvent":"mouseleave","closeOnOutsideClick":true,"position":"bottom","contentTemplate":$("#tpLeyenda-contentTemplate")});}).bind(this, jQuery));</script>
    <div id="tpInfDia"></div><script>DevExpress.utils.readyCallbacks.add((function($){$("#tpInfDia").dxTooltip({"hideEvent":"mouseleave","closeOnOutsideClick":true,"position":"bottom","contentTemplate":$("#tpInfDia-contentTemplate")});}).bind(this, jQuery));</script>
</div></div>

<div id="popupMonday"><div id="formMonday"></div><script type="text/html" id="tpLeyenda-contentTemplate">
            <div style="text-align: left;">
            <span><p><b>AP:</b> Asuntos propios</p></span>
            <span><p><b>AUS:</b> Otras ausencias</p></span>
            <span><p><b>EJ:</b> Exceso jornada</p></span>
            <span><p><b>ERE:</b> Expediente regulación empleo</p></span>
            <span><p><b>ERTE:</b> Expediente regulación empleo temporal</p></span>
            <span><p><b>FRC:</b> Festivo recuperable</p></span>
            <span><p><b>HC:</b> Horas de compensación por intervención</p></span>
            <span><p><b>HOEX:</b> Horas extras <b>aprobadas</b> no cerradas</p></span>
            <span><p><b>HOEXe:</b> HH.EE. cerradas Comp. económica</p></span>
            <span><p><b>HOEXm:</b> HH.EE. cerradas Comp. mixta</p></span>
            <span><p><b>HOEXt:</b> HH.EE. cerradas Comp. tiempo</p></span>
            <span><p><b>IT:</b> Incapacidad temporal</p></span>
            <span><p><b>JI:</b> Jornada industrial</p></span>
            <span><p><b>JL:</b> Jornada laboral</p></span>
            <span><p><b>JLv:</b> Jornada laboral validada empleado</p></span>
            <span><p><b>PC:</b> Pausa de comida</p></span>
            <span><p><b>V:</b> Vacaciones</p></span>
            <span><p><b>VAR:</b> Horas de intervención no cerradas</p></span>
            <span><p><b>VARc:</b> Horas de intervención complementarias</p></span>
            <span><p><b>VARe:</b> Horas de intervención Comp. económica</p></span>
            <span><p><b>VARm:</b> Horas de intervención Comp. mixta</p></span>
            <span><p><b>VARt:</b> Horas de intervención Comp. tiempo</p></span>
            <span><p><b>VG:</b> Vigília</p></span>
            <span><p><b>VI:</b> Vísperas</p></span>
        </div>
    </script><script type="text/html" id="tpInfDia-contentTemplate">
            <div id="tpInfDiaCont" style="text-align: left;">
                Jornada laboral teórica: <br>
                Absentismos no visibles: <br>
                Jornada laboral imputable: <br>
                Absentimos imputables:
            </div>
    </script><script>DevExpress.utils.readyCallbacks.add((function($){$("#formMonday").dxForm({"labelMode":"static","labelLocation":"left","items":[{"itemType":"simple","dataField":"Id","validationRules":[{"type":"required","message":"The Id field is required."}],"visible":false},{"itemType":"simple","dataField":"IdCalEmp","editorOptions":{},"editorType":"dxNumberBox","label":{"text":"Calendario"},"validationRules":[{"type":"required","message":"Calendario es obligatorio/a"}],"visible":false},{"itemType":"simple","dataField":"IdJornada","editorOptions":{},"editorType":"dxNumberBox","label":{"text":"Jornada"},"visible":false},{"itemType":"simple","dataField":"Dia","editorOptions":{"type":"date","readOnly":true},"editorType":"dxDateBox","label":{"text":"Dia"},"validationRules":[{"type":"required","message":"Dia es obligatorio/a"}]},{"itemType":"simple","dataField":"Festivo","editorOptions":{},"editorType":"dxCheckBox","label":{"text":"Festivo"},"visible":false},{"itemType":"simple","dataField":"It","editorOptions":{},"editorType":"dxCheckBox","label":{"text":"It"},"visible":false},{"itemType":"simple","dataField":"FechaValEmp","editorOptions":{},"editorType":"dxDateBox","label":{"text":"Fecha validación empleado"},"visible":false},{"itemType":"simple","dataField":"FechaValRRHH","editorOptions":{},"editorType":"dxDateBox","label":{"text":"Fecha validación RRHH"},"visible":false},{"itemType":"simple","dataField":"Ocupacion","validationRules":[{"type":"required","message":"The Ocupacion field is required."}],"visible":false},{"itemType":"simple","dataField":"FecIniComp","editorOptions":{},"editorType":"dxDateBox","validationRules":[{"type":"required","message":"The FecIniComp field is required."}],"visible":false},{"itemType":"simple","dataField":"FecFinComp","editorOptions":{},"editorType":"dxDateBox","validationRules":[{"type":"required","message":"The FecFinComp field is required."}],"visible":false},{"itemType":"simple","dataField":"IdCalendario","editorOptions":{},"editorType":"dxNumberBox","label":{"text":"Calendario"},"validationRules":[{"type":"required","message":"Calendario es obligatorio/a"}],"visible":false},{"itemType":"group","colCount":2,"items":[{"itemType":"button","horizontalAlignment":"right","colSpan":3,"buttonOptions":{"text":"Aceptar","useSubmitBehavior":false,"onClick":function (){ submitTotalTime('Monday'); },"type":"success"}},{"itemType":"button","horizontalAlignment":"left","buttonOptions":{"text":"Cancelar","useSubmitBehavior":false,"onClick":function (){ $('#popupMonday').dxPopup('instance').hide(); }}}]}]});}).bind(this, jQuery));</script></div><script>DevExpress.utils.readyCallbacks.add((function($){$("#popupMonday").dxPopup({"title":"Registro de pausas adicionales","height":240.0,"width":320.0,"onShown":verificarEstado,"visible":false,"dragEnabled":false,"closeOnOutsideClick":false,"shading":true});}).bind(this, jQuery));</script>

<div id="popupTuesday"><div id="formTuesday"></div><script>DevExpress.utils.readyCallbacks.add((function($){$("#formTuesday").dxForm({"labelMode":"static","labelLocation":"left","items":[{"itemType":"simple","dataField":"Id","validationRules":[{"type":"required","message":"The Id field is required."}],"visible":false},{"itemType":"simple","dataField":"IdCalEmp","editorOptions":{},"editorType":"dxNumberBox","label":{"text":"Calendario"},"validationRules":[{"type":"required","message":"Calendario es obligatorio/a"}],"visible":false},{"itemType":"simple","dataField":"IdJornada","editorOptions":{},"editorType":"dxNumberBox","label":{"text":"Jornada"},"visible":false},{"itemType":"simple","dataField":"Dia","editorOptions":{"type":"date","readOnly":true},"editorType":"dxDateBox","label":{"text":"Dia"},"validationRules":[{"type":"required","message":"Dia es obligatorio/a"}]},{"itemType":"simple","dataField":"Festivo","editorOptions":{},"editorType":"dxCheckBox","label":{"text":"Festivo"},"visible":false},{"itemType":"simple","dataField":"It","editorOptions":{},"editorType":"dxCheckBox","label":{"text":"It"},"visible":false},{"itemType":"simple","dataField":"FechaValEmp","editorOptions":{},"editorType":"dxDateBox","label":{"text":"Fecha validación empleado"},"visible":false},{"itemType":"simple","dataField":"FechaValRRHH","editorOptions":{},"editorType":"dxDateBox","label":{"text":"Fecha validación RRHH"},"visible":false},{"itemType":"simple","dataField":"Ocupacion","validationRules":[{"type":"required","message":"The Ocupacion field is required."}],"visible":false},{"itemType":"simple","dataField":"FecIniComp","editorOptions":{},"editorType":"dxDateBox","validationRules":[{"type":"required","message":"The FecIniComp field is required."}],"visible":false},{"itemType":"simple","dataField":"FecFinComp","editorOptions":{},"editorType":"dxDateBox","validationRules":[{"type":"required","message":"The FecFinComp field is required."}],"visible":false},{"itemType":"simple","dataField":"IdCalendario","editorOptions":{},"editorType":"dxNumberBox","label":{"text":"Calendario"},"validationRules":[{"type":"required","message":"Calendario es obligatorio/a"}],"visible":false},{"itemType":"group","colCount":2,"items":[{"itemType":"button","horizontalAlignment":"right","colSpan":3,"buttonOptions":{"text":"Aceptar","useSubmitBehavior":false,"onClick":function (){ submitTotalTime('Tuesday'); },"type":"success"}},{"itemType":"button","horizontalAlignment":"left","buttonOptions":{"text":"Cancelar","useSubmitBehavior":false,"onClick":function (){ $('#popupTuesday').dxPopup('instance').hide(); }}}]}]});}).bind(this, jQuery));</script></div><script>DevExpress.utils.readyCallbacks.add((function($){$("#popupTuesday").dxPopup({"title":"Registro de pausas adicionales","height":240.0,"width":320.0,"onShown":verificarEstado,"visible":false,"dragEnabled":false,"closeOnOutsideClick":false,"shading":true});}).bind(this, jQuery));</script>

<div id="popupWednesday"><div id="formWednesday"></div><script>DevExpress.utils.readyCallbacks.add((function($){$("#formWednesday").dxForm({"labelMode":"static","labelLocation":"left","items":[{"itemType":"simple","dataField":"Id","validationRules":[{"type":"required","message":"The Id field is required."}],"visible":false},{"itemType":"simple","dataField":"IdCalEmp","editorOptions":{},"editorType":"dxNumberBox","label":{"text":"Calendario"},"validationRules":[{"type":"required","message":"Calendario es obligatorio/a"}],"visible":false},{"itemType":"simple","dataField":"IdJornada","editorOptions":{},"editorType":"dxNumberBox","label":{"text":"Jornada"},"visible":false},{"itemType":"simple","dataField":"Dia","editorOptions":{"type":"date","readOnly":true},"editorType":"dxDateBox","label":{"text":"Dia"},"validationRules":[{"type":"required","message":"Dia es obligatorio/a"}]},{"itemType":"simple","dataField":"Festivo","editorOptions":{},"editorType":"dxCheckBox","label":{"text":"Festivo"},"visible":false},{"itemType":"simple","dataField":"It","editorOptions":{},"editorType":"dxCheckBox","label":{"text":"It"},"visible":false},{"itemType":"simple","dataField":"FechaValEmp","editorOptions":{},"editorType":"dxDateBox","label":{"text":"Fecha validación empleado"},"visible":false},{"itemType":"simple","dataField":"FechaValRRHH","editorOptions":{},"editorType":"dxDateBox","label":{"text":"Fecha validación RRHH"},"visible":false},{"itemType":"simple","dataField":"Ocupacion","validationRules":[{"type":"required","message":"The Ocupacion field is required."}],"visible":false},{"itemType":"simple","dataField":"FecIniComp","editorOptions":{},"editorType":"dxDateBox","validationRules":[{"type":"required","message":"The FecIniComp field is required."}],"visible":false},{"itemType":"simple","dataField":"FecFinComp","editorOptions":{},"editorType":"dxDateBox","validationRules":[{"type":"required","message":"The FecFinComp field is required."}],"visible":false},{"itemType":"simple","dataField":"IdCalendario","editorOptions":{},"editorType":"dxNumberBox","label":{"text":"Calendario"},"validationRules":[{"type":"required","message":"Calendario es obligatorio/a"}],"visible":false},{"itemType":"group","colCount":2,"items":[{"itemType":"button","horizontalAlignment":"right","colSpan":3,"buttonOptions":{"text":"Aceptar","useSubmitBehavior":false,"onClick":function (){ submitTotalTime('Wednesday'); },"type":"success"}},{"itemType":"button","horizontalAlignment":"left","buttonOptions":{"text":"Cancelar","useSubmitBehavior":false,"onClick":function (){ $('#popupWednesday').dxPopup('instance').hide(); }}}]}]});}).bind(this, jQuery));</script></div><script>DevExpress.utils.readyCallbacks.add((function($){$("#popupWednesday").dxPopup({"title":"Registro de pausas adicionales","height":240.0,"width":320.0,"onShown":verificarEstado,"visible":false,"dragEnabled":false,"closeOnOutsideClick":false,"shading":true});}).bind(this, jQuery));</script>

<div id="popupThursday"><div id="formThursday"></div><script>DevExpress.utils.readyCallbacks.add((function($){$("#formThursday").dxForm({"labelMode":"static","labelLocation":"left","items":[{"itemType":"simple","dataField":"Id","validationRules":[{"type":"required","message":"The Id field is required."}],"visible":false},{"itemType":"simple","dataField":"IdCalEmp","editorOptions":{},"editorType":"dxNumberBox","label":{"text":"Calendario"},"validationRules":[{"type":"required","message":"Calendario es obligatorio/a"}],"visible":false},{"itemType":"simple","dataField":"IdJornada","editorOptions":{},"editorType":"dxNumberBox","label":{"text":"Jornada"},"visible":false},{"itemType":"simple","dataField":"Dia","editorOptions":{"type":"date","readOnly":true},"editorType":"dxDateBox","label":{"text":"Dia"},"validationRules":[{"type":"required","message":"Dia es obligatorio/a"}]},{"itemType":"simple","dataField":"Festivo","editorOptions":{},"editorType":"dxCheckBox","label":{"text":"Festivo"},"visible":false},{"itemType":"simple","dataField":"It","editorOptions":{},"editorType":"dxCheckBox","label":{"text":"It"},"visible":false},{"itemType":"simple","dataField":"FechaValEmp","editorOptions":{},"editorType":"dxDateBox","label":{"text":"Fecha validación empleado"},"visible":false},{"itemType":"simple","dataField":"FechaValRRHH","editorOptions":{},"editorType":"dxDateBox","label":{"text":"Fecha validación RRHH"},"visible":false},{"itemType":"simple","dataField":"Ocupacion","validationRules":[{"type":"required","message":"The Ocupacion field is required."}],"visible":false},{"itemType":"simple","dataField":"FecIniComp","editorOptions":{},"editorType":"dxDateBox","validationRules":[{"type":"required","message":"The FecIniComp field is required."}],"visible":false},{"itemType":"simple","dataField":"FecFinComp","editorOptions":{},"editorType":"dxDateBox","validationRules":[{"type":"required","message":"The FecFinComp field is required."}],"visible":false},{"itemType":"simple","dataField":"IdCalendario","editorOptions":{},"editorType":"dxNumberBox","label":{"text":"Calendario"},"validationRules":[{"type":"required","message":"Calendario es obligatorio/a"}],"visible":false},{"itemType":"group","colCount":2,"items":[{"itemType":"button","horizontalAlignment":"right","colSpan":3,"buttonOptions":{"text":"Aceptar","useSubmitBehavior":false,"onClick":function (){ submitTotalTime('Thursday'); },"type":"success"}},{"itemType":"button","horizontalAlignment":"left","buttonOptions":{"text":"Cancelar","useSubmitBehavior":false,"onClick":function (){ $('#popupThursday').dxPopup('instance').hide(); }}}]}]});}).bind(this, jQuery));</script></div><script>DevExpress.utils.readyCallbacks.add((function($){$("#popupThursday").dxPopup({"title":"Registro de pausas adicionales","height":240.0,"width":320.0,"onShown":verificarEstado,"visible":false,"dragEnabled":false,"closeOnOutsideClick":false,"shading":true});}).bind(this, jQuery));</script>

<div id="popupFriday"><div id="formFriday"></div><script>DevExpress.utils.readyCallbacks.add((function($){$("#formFriday").dxForm({"labelMode":"static","labelLocation":"left","items":[{"itemType":"simple","dataField":"Id","validationRules":[{"type":"required","message":"The Id field is required."}],"visible":false},{"itemType":"simple","dataField":"IdCalEmp","editorOptions":{},"editorType":"dxNumberBox","label":{"text":"Calendario"},"validationRules":[{"type":"required","message":"Calendario es obligatorio/a"}],"visible":false},{"itemType":"simple","dataField":"IdJornada","editorOptions":{},"editorType":"dxNumberBox","label":{"text":"Jornada"},"visible":false},{"itemType":"simple","dataField":"Dia","editorOptions":{"type":"date","readOnly":true},"editorType":"dxDateBox","label":{"text":"Dia"},"validationRules":[{"type":"required","message":"Dia es obligatorio/a"}]},{"itemType":"simple","dataField":"Festivo","editorOptions":{},"editorType":"dxCheckBox","label":{"text":"Festivo"},"visible":false},{"itemType":"simple","dataField":"It","editorOptions":{},"editorType":"dxCheckBox","label":{"text":"It"},"visible":false},{"itemType":"simple","dataField":"FechaValEmp","editorOptions":{},"editorType":"dxDateBox","label":{"text":"Fecha validación empleado"},"visible":false},{"itemType":"simple","dataField":"FechaValRRHH","editorOptions":{},"editorType":"dxDateBox","label":{"text":"Fecha validación RRHH"},"visible":false},{"itemType":"simple","dataField":"Ocupacion","validationRules":[{"type":"required","message":"The Ocupacion field is required."}],"visible":false},{"itemType":"simple","dataField":"FecIniComp","editorOptions":{},"editorType":"dxDateBox","validationRules":[{"type":"required","message":"The FecIniComp field is required."}],"visible":false},{"itemType":"simple","dataField":"FecFinComp","editorOptions":{},"editorType":"dxDateBox","validationRules":[{"type":"required","message":"The FecFinComp field is required."}],"visible":false},{"itemType":"simple","dataField":"IdCalendario","editorOptions":{},"editorType":"dxNumberBox","label":{"text":"Calendario"},"validationRules":[{"type":"required","message":"Calendario es obligatorio/a"}],"visible":false},{"itemType":"group","colCount":2,"items":[{"itemType":"button","horizontalAlignment":"right","colSpan":3,"buttonOptions":{"text":"Aceptar","useSubmitBehavior":false,"onClick":function (){ submitTotalTime('Friday'); },"type":"success"}},{"itemType":"button","horizontalAlignment":"left","buttonOptions":{"text":"Cancelar","useSubmitBehavior":false,"onClick":function (){ $('#popupFriday').dxPopup('instance').hide(); }}}]}]});}).bind(this, jQuery));</script></div><script>DevExpress.utils.readyCallbacks.add((function($){$("#popupFriday").dxPopup({"title":"Registro de pausas adicionales","height":240.0,"width":320.0,"onShown":verificarEstado,"visible":false,"dragEnabled":false,"closeOnOutsideClick":false,"shading":true});}).bind(this, jQuery));</script>

<div id="popupSaturday"><div id="formSaturday"></div><script>DevExpress.utils.readyCallbacks.add((function($){$("#formSaturday").dxForm({"labelMode":"static","labelLocation":"left","items":[{"itemType":"simple","dataField":"Id","validationRules":[{"type":"required","message":"The Id field is required."}],"visible":false},{"itemType":"simple","dataField":"IdCalEmp","editorOptions":{},"editorType":"dxNumberBox","label":{"text":"Calendario"},"validationRules":[{"type":"required","message":"Calendario es obligatorio/a"}],"visible":false},{"itemType":"simple","dataField":"IdJornada","editorOptions":{},"editorType":"dxNumberBox","label":{"text":"Jornada"},"visible":false},{"itemType":"simple","dataField":"Dia","editorOptions":{"type":"date","readOnly":true},"editorType":"dxDateBox","label":{"text":"Dia"},"validationRules":[{"type":"required","message":"Dia es obligatorio/a"}]},{"itemType":"simple","dataField":"Festivo","editorOptions":{},"editorType":"dxCheckBox","label":{"text":"Festivo"},"visible":false},{"itemType":"simple","dataField":"It","editorOptions":{},"editorType":"dxCheckBox","label":{"text":"It"},"visible":false},{"itemType":"simple","dataField":"FechaValEmp","editorOptions":{},"editorType":"dxDateBox","label":{"text":"Fecha validación empleado"},"visible":false},{"itemType":"simple","dataField":"FechaValRRHH","editorOptions":{},"editorType":"dxDateBox","label":{"text":"Fecha validación RRHH"},"visible":false},{"itemType":"simple","dataField":"Ocupacion","validationRules":[{"type":"required","message":"The Ocupacion field is required."}],"visible":false},{"itemType":"simple","dataField":"FecIniComp","editorOptions":{},"editorType":"dxDateBox","validationRules":[{"type":"required","message":"The FecIniComp field is required."}],"visible":false},{"itemType":"simple","dataField":"FecFinComp","editorOptions":{},"editorType":"dxDateBox","validationRules":[{"type":"required","message":"The FecFinComp field is required."}],"visible":false},{"itemType":"simple","dataField":"IdCalendario","editorOptions":{},"editorType":"dxNumberBox","label":{"text":"Calendario"},"validationRules":[{"type":"required","message":"Calendario es obligatorio/a"}],"visible":false},{"itemType":"group","colCount":2,"items":[{"itemType":"button","horizontalAlignment":"right","colSpan":3,"buttonOptions":{"text":"Aceptar","useSubmitBehavior":false,"onClick":function (){ submitTotalTime('Saturday'); },"type":"success"}},{"itemType":"button","horizontalAlignment":"left","buttonOptions":{"text":"Cancelar","useSubmitBehavior":false,"onClick":function (){ $('#popupSaturday').dxPopup('instance').hide(); }}}]}]});}).bind(this, jQuery));</script></div><script>DevExpress.utils.readyCallbacks.add((function($){$("#popupSaturday").dxPopup({"title":"Registro de pausas adicionales","height":240.0,"width":320.0,"onShown":verificarEstado,"visible":false,"dragEnabled":false,"closeOnOutsideClick":false,"shading":true});}).bind(this, jQuery));</script>

<div id="popupSunday"><div id="formSunday"></div><script>DevExpress.utils.readyCallbacks.add((function($){$("#formSunday").dxForm({"labelMode":"static","labelLocation":"left","items":[{"itemType":"simple","dataField":"Id","validationRules":[{"type":"required","message":"The Id field is required."}],"visible":false},{"itemType":"simple","dataField":"IdCalEmp","editorOptions":{},"editorType":"dxNumberBox","label":{"text":"Calendario"},"validationRules":[{"type":"required","message":"Calendario es obligatorio/a"}],"visible":false},{"itemType":"simple","dataField":"IdJornada","editorOptions":{},"editorType":"dxNumberBox","label":{"text":"Jornada"},"visible":false},{"itemType":"simple","dataField":"Dia","editorOptions":{"type":"date","readOnly":true},"editorType":"dxDateBox","label":{"text":"Dia"},"validationRules":[{"type":"required","message":"Dia es obligatorio/a"}]},{"itemType":"simple","dataField":"Festivo","editorOptions":{},"editorType":"dxCheckBox","label":{"text":"Festivo"},"visible":false},{"itemType":"simple","dataField":"It","editorOptions":{},"editorType":"dxCheckBox","label":{"text":"It"},"visible":false},{"itemType":"simple","dataField":"FechaValEmp","editorOptions":{},"editorType":"dxDateBox","label":{"text":"Fecha validación empleado"},"visible":false},{"itemType":"simple","dataField":"FechaValRRHH","editorOptions":{},"editorType":"dxDateBox","label":{"text":"Fecha validación RRHH"},"visible":false},{"itemType":"simple","dataField":"Ocupacion","validationRules":[{"type":"required","message":"The Ocupacion field is required."}],"visible":false},{"itemType":"simple","dataField":"FecIniComp","editorOptions":{},"editorType":"dxDateBox","validationRules":[{"type":"required","message":"The FecIniComp field is required."}],"visible":false},{"itemType":"simple","dataField":"FecFinComp","editorOptions":{},"editorType":"dxDateBox","validationRules":[{"type":"required","message":"The FecFinComp field is required."}],"visible":false},{"itemType":"simple","dataField":"IdCalendario","editorOptions":{},"editorType":"dxNumberBox","label":{"text":"Calendario"},"validationRules":[{"type":"required","message":"Calendario es obligatorio/a"}],"visible":false},{"itemType":"group","colCount":2,"items":[{"itemType":"button","horizontalAlignment":"right","colSpan":3,"buttonOptions":{"text":"Aceptar","useSubmitBehavior":false,"onClick":function (){ submitTotalTime('Sunday'); },"type":"success"}},{"itemType":"button","horizontalAlignment":"left","buttonOptions":{"text":"Cancelar","useSubmitBehavior":false,"onClick":function (){ $('#popupSunday').dxPopup('instance').hide(); }}}]}]});}).bind(this, jQuery));</script></div><script>DevExpress.utils.readyCallbacks.add((function($){$("#popupSunday").dxPopup({"title":"Registro de pausas adicionales","height":240.0,"width":320.0,"onShown":verificarEstado,"visible":false,"dragEnabled":false,"closeOnOutsideClick":false,"shading":true});}).bind(this, jQuery));</script>

<div id="calendario"></div><script>DevExpress.utils.readyCallbacks.add((function($){$("#calendario").dxScheduler({"dataSource":{"store":DevExpress.data.AspNet.createStore({"key":"Id","errorHandler":dataSourceLoadErrorGeneral,"loadParams":{"IdEmpleado":15612,"view":GetView},"onLoaded":refreshValidateForm,"onBeforeSend":OnBeforeSendCalendarioEmpleado,"loadMode":"processed","cacheRawData":false,"loadUrl":"/TWTime/Calendario/GetCalendarioEmpleado","insertUrl":"/TWTime/Calendario/InsertCalendarioEmpleado","updateUrl":"/TWTime/Calendario/UpdateCalendarioEmpleado","deleteUrl":"/TWTime/Calendario/DeleteCalendarioEmpleado"}),"onLoadError":dataSourceLoadErrorGeneral},"textExpr":"Text","descriptionExpr":"Descripcion","startDateExpr":"StartDate","endDateExpr":"EndDate","views":[{"name":"Vista Semanal","type":"week","groupOrientation":"horizontal","dateCellTemplate":dateCellTemplate,"dataCellTemplate":dataCellTemplate,"timeCellTemplate":timeCellTemplate},{"name":"Vista Mensual","type":"month","maxAppointmentsPerCell":"unlimited","dateCellTemplate":dateCellTemplateMonth,"dataCellTemplate":MonthdataCellTemplate,"appointmentTemplate":$("#calendario-views1-appointmentTemplate")}],"editing":{"allowDragging":true,"allowAdding":false,"allowUpdating":true,"allowDeleting":true},"resources":[{"fieldExpr":"Color","valueExpr":"Codigo","colorExpr":"Color","dataSource":{"store":new DevExpress.data.ArrayStore({"data":[{"Codigo":"V","Color":"#00FFFF","Descripcion":"V"},{"Codigo":"AP","Color":"#00FFFF","Descripcion":"AP"},{"Codigo":"JI","Color":"#00FFFF","Descripcion":"JI"},{"Codigo":"EJ","Color":"#00FFFF","Descripcion":"EJ"},{"Codigo":"VI","Color":"#00FFFF","Descripcion":"VI"},{"Codigo":"IT","Color":"#FFFFE0","Descripcion":"IT"},{"Codigo":"AUS","Color":"#FFA500","Descripcion":"AUS"},{"Codigo":"JL","Color":"#EDFBD8","Descripcion":"JL"},{"Codigo":"VAR","Color":"#FF0000","Descripcion":"VAR"},{"Codigo":"VARc","Color":"#B87EB1","Descripcion":"VARc"},{"Codigo":"VARe","Color":"#B87EB1","Descripcion":"VARe"},{"Codigo":"VARm","Color":"#e69edc","Descripcion":"VARm"},{"Codigo":"VARt","Color":"#e69edc","Descripcion":"VARt"},{"Codigo":"JLv","Color":"#CECED4","Descripcion":"JLv"},{"Codigo":"VG","Color":"#3B84A3","Descripcion":"VG"},{"Codigo":"HC","Color":"#00FFFF","Descripcion":"HC"},{"Codigo":"ERE","Color":"#934F0B","Descripcion":"ERE"},{"Codigo":"ERTE","Color":"#BF670F","Descripcion":"ERTE"},{"Codigo":"PC","Color":"#B6EF99","Descripcion":"PC"},{"Codigo":"FRC","Color":"#663BA3","Descripcion":"FRC"},{"Codigo":"HOEX","Color":"#FF0000","Descripcion":"HOEX"},{"Codigo":"HOEXe","Color":"#B87EB1","Descripcion":"HOEXe"},{"Codigo":"HOEXm","Color":"#e69edc","Descripcion":"HOEXm"},{"Codigo":"HOEXt","Color":"#e69edc","Descripcion":"HOEXt"}]})}}],"appointmentTooltipTemplate":$("#calendario-appointmentTooltipTemplate"),"appointmentTemplate":$("#calendario-appointmentTemplate"),"onAppointmentFormOpening":onFormOpening,"onAppointmentClick":disableDblClick,"onAppointmentDblClick":disableDblClick,"onCellClick":disableDblClick,"onAppointmentDeleting":deleteAppointment,"onAppointmentUpdating":showAppointment,"remoteFiltering":true,"useDropDownViewSwitcher":false,"currentView":"week","onContentReady":onContentReady,"currentDate":new Date(2025, 5, 3, 11, 20, 10, 563),"firstDayOfWeek":1,"showAllDayPanel":false,"height":"750","showCurrentTimeIndicator":false,"onOptionChanged":onCalendarioInitialized});}).bind(this, jQuery));</script>



<script>
    var parametrizacionSemana = null;
    var strDefaultJL = `{"Id":-9999,"IdCalEmpDia":0,"IdCalEmp":0,"IdCompensacion":0,"IdJornada":null,"IdTipoRegistro":1,"IdEmpleado":15612,"Activo":false,"Year":0,"HoraInicio":"00:00","PausaTotal":null,"PausaTotalHoras":0.0,"HoraFin":"00:00","Text":"JL","StartDate":"1980-01-01T00:00:00+01:00","HoraInicioTime":"00:00:00","HoraFinTime":"00:00:00","EndDate":"1980-01-01T00:00:00+01:00","Dia":"1980-01-01T00:00:00+01:00","It":false,"Festivo":false,"FEsp":false,"Descripcion":"Jornada laboral","Timestamp":"0001-01-01T00:00:00+00:00","FechaValEmp":null,"FechaValRRHH":null,"CanValidate":true,"Editable":true,"Codigo":"JL","Color":"JL","CodigoColor":null,"Ocupacion":0.0,"FecIniComp":"0001-01-01T00:00:00","FecFinComp":"0001-01-01T00:00:00","IdCalendario":0,"IsDeletable":false,"EsRevEmp":false,"EsValEmp":false,"TotalHorasSinDescanso":0.0,"TotalHorasSinDescansoTimeSpan":"00:00:00","PausaTotalHorasTimeSpan":"00:00:00","Turnos":null,"IsPartitionable":false,"Particionar":false,"AbsCod":null,"HorasFestRec":null,"SemanaActual":0,"SemanaFutura":0,"Ualta":0,"UaltaNom":null,"UaltaLog":null,"Falta":"0001-01-01T00:00:00","Umod":0,"UmodNom":null,"UmodLog":null,"Fmod":"0001-01-01T00:00:00"}`;
    var regDefaultJL = JSON.parse(strDefaultJL);
    var strDefaultPC = `{"Id":-9999,"IdCalEmpDia":0,"IdCalEmp":0,"IdCompensacion":0,"IdJornada":null,"IdTipoRegistro":6,"IdEmpleado":15612,"Activo":false,"Year":0,"HoraInicio":"00:00","PausaTotal":null,"PausaTotalHoras":0.0,"HoraFin":"00:00","Text":"PC","StartDate":"1980-01-01T00:00:00+01:00","HoraInicioTime":"00:00:00","HoraFinTime":"00:00:00","EndDate":"1980-01-01T00:00:00+01:00","Dia":"1980-01-01T00:00:00+01:00","It":false,"Festivo":false,"FEsp":false,"Descripcion":"Pausa de comida","Timestamp":"0001-01-01T00:00:00+00:00","FechaValEmp":null,"FechaValRRHH":null,"CanValidate":true,"Editable":true,"Codigo":"PC","Color":"PC","CodigoColor":null,"Ocupacion":0.0,"FecIniComp":"0001-01-01T00:00:00","FecFinComp":"0001-01-01T00:00:00","IdCalendario":0,"IsDeletable":false,"EsRevEmp":false,"EsValEmp":false,"TotalHorasSinDescanso":0.0,"TotalHorasSinDescansoTimeSpan":"00:00:00","PausaTotalHorasTimeSpan":"00:00:00","Turnos":null,"IsPartitionable":false,"Particionar":false,"AbsCod":null,"HorasFestRec":null,"SemanaActual":0,"SemanaFutura":0,"Ualta":0,"UaltaNom":null,"UaltaLog":null,"Falta":"0001-01-01T00:00:00","Umod":0,"UmodNom":null,"UmodLog":null,"Fmod":"0001-01-01T00:00:00"}`;
    var regDefaultPC = JSON.parse(strDefaultPC);
    var turnos = false; //No se usa

    function timeCellTemplate(itemData, itemIndex, itemElement) {
        const element = $(`<div>${itemData.text}</div>`);
        const { date } = itemData;
        if (itemIndex == 0) {
            element.attr('id', 'firstCalendarTimeRow');
        }

        if (isFocusTime(date)) {
            element.attr('id', 'focusCalendarTime');
        }

        return itemElement.append(element);
    }
    function isFocusTime(date) {
        return date.getHours() == 16;
    }

    function onContentReady(args) {        
        var scroll = args.component.getWorkSpaceScrollable();
        var scrollInstance = scroll.instance();
        var focusElement = $('#focusCalendarTime')[0];
        if (focusElement !== undefined) {
            scrollInstance.scrollToElement($('#focusCalendarTime')[0])
        }
    }

    function onCalendarioInitialized(s, e, e2) {
        if (s.name === "currentView") 
        {
            if (s.component.option("currentView") == "month" || s.component.option("currentView").toLowerCase() == "vista mensual") {
                $('#CERepliWeek').css('display', 'none');
                $('#VisorHoras').css('display', "none");
                $('#CEValWeek').css('display', "none");
                $('#regularizarSemana')?.css('display', "none");
                $('#tituloEmpleado').css('margin-left', '227px');
            }
            else {
                //$('#CERepliWeek').css('display', (calEditable ? 'block' : 'none'));
                $('#CERepliWeek').css('display', 'none');
                $('#VisorHoras').css('display', 'block');
                $('#CEValWeek').css('display', "block");
                $('#regularizarSemana')?.css('display', "block");
                $('#tituloEmpleado').css('margin-left', '-120px');
            }
        }
    }

    function GetView() {
        var nameView = $("#calendario").dxScheduler('instance').option("currentView");
        if (nameView == "month" || nameView.toLowerCase() == "vista mensual") {
            nameView = "month";
        }
        else {
            nameView = "week";
        }
        return $("#calendario").dxScheduler('instance').option("currentView");
    }

    function OnBeforeSendCalendarioEmpleado(e, ajax) {
        if (e === 'load') {   
            $('#CEValWeek').css('display', "none");
            $('#regularizarSemana')?.css('display', "none");
            var jsonArray = JSON.parse(ajax.data.filter);
            lunesSemana = new Date(getLastStarDate(jsonArray)).toLocaleDateString('es-ES');
        
         }
    }

    function getLastStarDate(array)
    {
         let ultimoStartDate = null;

    // Función recursiva para buscar 'StartDate'
    function buscarStartDate(arr) {
      for (let i = 0; i < arr.length; i++) {
        if (Array.isArray(arr[i])) {
          buscarStartDate(arr[i]);
        } else if (arr[i] === "StartDate" && Array.isArray(arr) && arr.length > (i + 1)) {
          ultimoStartDate = arr[i + 1];
        }
      }
    }

    buscarStartDate(array);
    return ultimoStartDate;
    }
    
</script>
<script>

    var context = JSON.parse(`{"Agenda":[{"Dia":"2024-04-08T00:00:00+02:00","Festivo":true,"Trabajable":false},{"Dia":"2024-05-01T00:00:00+02:00","Festivo":true,"Trabajable":false},{"Dia":"2024-08-15T00:00:00+02:00","Festivo":true,"Trabajable":false},{"Dia":"2024-10-09T00:00:00+02:00","Festivo":true,"Trabajable":false},{"Dia":"2024-10-12T00:00:00+02:00","Festivo":true,"Trabajable":false},{"Dia":"2024-11-01T00:00:00+01:00","Festivo":true,"Trabajable":false},{"Dia":"2024-12-06T00:00:00+01:00","Festivo":true,"Trabajable":false},{"Dia":"2024-12-25T00:00:00+01:00","Festivo":true,"Trabajable":false},{"Dia":"2025-01-01T00:00:00+01:00","Festivo":true,"Trabajable":false},{"Dia":"2025-01-06T00:00:00+01:00","Festivo":true,"Trabajable":false},{"Dia":"2025-01-22T00:00:00+01:00","Festivo":true,"Trabajable":false},{"Dia":"2025-03-19T00:00:00+01:00","Festivo":true,"Trabajable":false},{"Dia":"2025-04-18T00:00:00+02:00","Festivo":true,"Trabajable":false},{"Dia":"2025-04-21T00:00:00+02:00","Festivo":true,"Trabajable":false},{"Dia":"2025-04-28T00:00:00+02:00","Festivo":true,"Trabajable":false},{"Dia":"2025-05-01T00:00:00+02:00","Festivo":true,"Trabajable":false},{"Dia":"2025-08-15T00:00:00+02:00","Festivo":true,"Trabajable":false},{"Dia":"2025-10-09T00:00:00+02:00","Festivo":true,"Trabajable":false},{"Dia":"2025-11-01T00:00:00+01:00","Festivo":true,"Trabajable":false},{"Dia":"2025-12-06T00:00:00+01:00","Festivo":true,"Trabajable":false},{"Dia":"2025-12-08T00:00:00+01:00","Festivo":true,"Trabajable":false},{"Dia":"2025-12-25T00:00:00+01:00","Festivo":true,"Trabajable":false}],"RecursosColores":[{"Codigo":"V","Color":"#00FFFF","Descripcion":"V"},{"Codigo":"AP","Color":"#00FFFF","Descripcion":"AP"},{"Codigo":"JI","Color":"#00FFFF","Descripcion":"JI"},{"Codigo":"EJ","Color":"#00FFFF","Descripcion":"EJ"},{"Codigo":"VI","Color":"#00FFFF","Descripcion":"VI"},{"Codigo":"IT","Color":"#FFFFE0","Descripcion":"IT"},{"Codigo":"AUS","Color":"#FFA500","Descripcion":"AUS"},{"Codigo":"JL","Color":"#EDFBD8","Descripcion":"JL"},{"Codigo":"VAR","Color":"#FF0000","Descripcion":"VAR"},{"Codigo":"VARc","Color":"#B87EB1","Descripcion":"VARc"},{"Codigo":"VARe","Color":"#B87EB1","Descripcion":"VARe"},{"Codigo":"VARm","Color":"#e69edc","Descripcion":"VARm"},{"Codigo":"VARt","Color":"#e69edc","Descripcion":"VARt"},{"Codigo":"JLv","Color":"#CECED4","Descripcion":"JLv"},{"Codigo":"VG","Color":"#3B84A3","Descripcion":"VG"},{"Codigo":"HC","Color":"#00FFFF","Descripcion":"HC"},{"Codigo":"ERE","Color":"#934F0B","Descripcion":"ERE"},{"Codigo":"ERTE","Color":"#BF670F","Descripcion":"ERTE"},{"Codigo":"PC","Color":"#B6EF99","Descripcion":"PC"},{"Codigo":"FRC","Color":"#663BA3","Descripcion":"FRC"},{"Codigo":"HOEX","Color":"#FF0000","Descripcion":"HOEX"},{"Codigo":"HOEXe","Color":"#B87EB1","Descripcion":"HOEXe"},{"Codigo":"HOEXm","Color":"#e69edc","Descripcion":"HOEXm"},{"Codigo":"HOEXt","Color":"#e69edc","Descripcion":"HOEXt"}],"Editable":true,"IdEmpleado":15612,"Nombre":"CHRISTIAN ALT ","Turnos":false}`);    
</script>

<script>
    function actualizarHorasSemana() {
        let objHoras = $.getJSON("https://t-worktime.t-systems.es/TWTime/Calendario/GetHorasSemana" + "?lunes=" + encodeURIComponent(lunesSemana) + "&IdEmpleado=15612")
            .done(function (data) {
                $('#txtHorasConvenio').dxTextBox('instance').option('value', data.HorasConvenio);
                $('#txtHorasConvenio').dxTextBox('instance').option('visible', !data.Turnos);
                $('#txtHorasReales').dxTextBox('instance').option('value', data.HorasReales);                
            })
            .fail(function (s, e, e2) {
                dataSourceLoadErrorGeneral(s.responseText);
            });
    }
    function resetDias() {
        var dia = new Date();
        for (var i = 0; i < 7; i++) {
            var nombre = dia.toLocaleDateString('en', { weekday: 'long' });
            $('#contador' + nombre).html("00:00");
            dia.setDate(dia.getDate() + 1);
        }
    }
    async function refreshValidateForm(s, e, e2) {
        //var data = e.component.option("dataSource").items();
        //parametrizacionSemana = data.Parametrizacion;
        //e.component.option("dataSource", data.Calendario);
        $('#loadPanelPortal').dxLoadPanel('instance').show();
        //Llamamos refrescar las horas
        actualizarHorasSemana();
        let SemanaActual = undefined;
        let SemanaFutura = undefined;
        if (s) {
            let isValidable = true;
            resetDias();
            s.forEach(i => {
                // if (SemanaActual == undefined){
                //     SemanaActual = i.SemanaActual;
                // }
                // if (SemanaFutura == undefined){
                //     SemanaFutura = i.SemanaFutura;
                // }
                if (!i.CanValidate) {
                    isValidable = false;
                }
                actualizarHorasDia(i);                
            });
            if(!isValidable)
            {
                $('div [cabBotdia]').map(function() {
                    $(this).css("display", "none");
                });
            }
            else
            {
                $('div [cabBotdia]').map(function() {
                    $(this).css("display", "inline-block");
                });
            }
            let esRevEmp = true;
            let esvalemp = false;
            s.forEach(i => {
                if (!i.EsRevEmp) {
                    esRevEmp = i.EsRevEmp;
                    esvalemp = i.EsValEmp;
                }
            });

            let datos;
            
            if (SemanaActual == undefined)
            {
                  try
                  {
                      // Realiza la solicitud GET usando fetch
                      // const respuesta = await fetch('https://t-worktime.t-systems.es/TWTime/Calendario/GetParametrizacion' + "?lunesSemana=" + encodeURIComponent(lunesSemana));

                      // // Verifica si la respuesta fue exitosa
                      // if (!respuesta.ok) {                           
                      //     let error = `Error al obtener la parametrización de la semana: ${respuesta.status}`;
                      //     dataSourceLoadErrorGeneral(error);
                      // }

                      // Convierte la respuesta a formato JSON
                      datos = parametrizacionSemana;
                      SemanaActual = datos.SemanaActual;
                      SemanaFutura = datos.SemanaFutura;

                      // Usa los datos obtenidos
                      console.log('Datos obtenidos:', datos);
                    
                  } catch (error) {
                        // Maneja errores en caso de que ocurran
                        console.error('Ocurrió un error:', error);
                  }
            }

            // if (SemanaActual == 1)
            // {
            //     $('#CERepliWeek').css('display', 'block');
            // }
            // else {
            //     $('#CERepliWeek').css('display', 'none');
            // }

            if (SemanaFutura == 0)
            {
                if (esRevEmp)
                {
                    $('#regularizarSemana').css('display', 'block');
                }   
                               
                $('#buttonMondayHT').css('display', (datos.LLimpio == 1 && datos.LReg == 1) ? 'inline-block' : 'none');                
                $('#buttonTuesdayHT').css('display', (datos.MLimpio == 1 && datos.MReg == 1) ? 'inline-block' : 'none');
                $('#buttonWednesdayHT').css('display', (datos.XLimpio == 1 && datos.XReg == 1) ? 'inline-block' : 'none');
                $('#buttonThursdayHT').css('display', (datos.JLimpio == 1 && datos.JReg == 1) ? 'inline-block' : 'none');
                $('#buttonFridayHT').css('display', (datos.VLimpio == 1 && datos.VReg == 1) ? 'inline-block' : 'none');
                $('#buttonSaturdayHT').css('display', (datos.SLimpio == 1 && datos.SReg == 1) ? 'inline-block' : 'none');                
                $('#buttonSundayHT').css('display', (datos.DLimpio == 1 && datos.DReg == 1) ? 'inline-block' : 'none');

                if(datos.LReg == 1)
                {
                    $('#buttonMondayCrear').css('display', 'inline-block');
                     $('#buttonMondayPC').css('display', 'inline-block');
                }
                else if (SemanaFutura == 0)
                {
                    $('#buttonMondayCrear').parent().css("height","35px")
                }
                if(datos.MReg == 1)
                {
                    $('#buttonTuesdayCrear').css('display', 'inline-block');
                    $('#buttonTuesdayPC').css('display', 'inline-block');
                }
                else if (SemanaFutura == 0)
                {
                    $('#buttonTuesdayCrear').parent().css("height","35px")
                }
                if(datos.XReg == 1)
                {
                    $('#buttonWednesdayCrear').css('display', 'inline-block');
                    $('#buttonWednesdayPC').css('display', 'inline-block');
                }
                else if (SemanaFutura == 0)
                {
                    $('#buttonWednesdayCrear').parent().css("height","35px")
                }
                if(datos.JReg == 1)
                {
                    $('#buttonThursdayCrear').css('display', 'inline-block');
                    $('#buttonThursdayPC').css('display', 'inline-block');
                }
                else if (SemanaFutura == 0)
                {
                    $('#buttonThursdayCrear').parent().css("height","35px")
                }
                if(datos.VReg == 1)
                {
                    $('#buttonFridayCrear').css('display', 'inline-block');
                    $('#buttonFridayPC').css('display', 'inline-block');
                }
                else if (SemanaFutura == 0)
                {
                    $('#buttonFridayCrear').parent().css("height","35px")
                }
                if(datos.SReg == 1)
                {
                    $('#buttonSaturdayCrear').css('display', 'inline-block');
                    $('#buttonSaturdayPC').css('display', 'inline-block');
                }
                else if (SemanaFutura == 0)
                {
                    $('#buttonSaturdayCrear').parent().css("height","35px")
                }
                if(datos.DReg == 1)
                {
                    $('#buttonSundayCrear').css('display', 'inline-block');
                    $('#buttonSundayPC').css('display', 'inline-block');
                }
                else if (SemanaFutura == 0)
                {
                    $('#buttonSundayCrear').parent().css("height","35px")
                }




                //  $('div[aria-label="clock"]').each(function() {
                //     $(this).css('display', 'inline-block');
                // });
                // $('div[aria-label="food"]').each(function() {
                //     $(this).css('display', 'inline-block');
                // });
            }
            else {
                $('#regularizarSemana').css('display', 'none');
                $('div[aria-label="clock"]').each(function() {
                    $(this).css('display', 'none');
                });
                $('div[aria-label="food"]').each(function() {
                    $(this).css('display', 'none');
                });
                $('div[aria-label="verticalaligncenter"]').each(function() {
                    $(this).css('display', 'none');
                });

            }


            isValidable = (isValidable && !esRevEmp) || esvalemp;

                        calEditable = isValidable;
                        $('#AccesoVariables').css('display', (isValidable ? 'block' : 'none'));
                        $('#regularizarSemana').css('display', (esRevEmp && SemanaFutura == 0) ? 'block' : 'none');
                        $('#IdRegularizarSemanaBtn').dxButton('instance').option("visible", esRevEmp && SemanaFutura == 0);
                        $('#CEValWeek').css('display', (isValidable && SemanaFutura == 0) ? "block" : "none");
                        $('#IdValidarSemana').dxButton('instance').option("visible", isValidable);
                        }
        $('#loadPanelPortal').dxLoadPanel('instance').hide();
    }
    
    var diaActualizado = "";
    function actualizarHorasDia(dia) {
        var nombre = new Date(dia.Dia).toLocaleDateString('en', { weekday: 'long' });
        let valorPausas = 0.0;
        if (diaActualizado != nombre) {
            diaActualizado = nombre;
            valorPausas = dia.PausaTotalHoras;
            $('#contador' + nombre).attr("pausas", valorPausas);
        }
        var value = $('#contador' + nombre).html();
        if (value !== undefined) {
            value = parseFloatHoraria(value);
            if (dia.IdTipoRegistro == 1) {
                value = value + dia.TotalHorasSinDescanso - valorPausas;
            }
            $('#contador' + nombre).html(value.horaMinutos());
        }        
    }    
</script>
<script>
    let lunesSemana = null;
    function submitTotalTime(weekday) {
        let form = $('#form' + weekday).dxForm('instance')
        let dataObj = form.option("formData");
    }
    function UpdateButtonName(weekday, value) {
        $button = $('#button' + weekday).dxButton('instance');
        $button.option('text', value + ' ' + `Pausas adicionales`);
    }

    var listHa = { Monday: null, Tuesday: null, Wednesday: null, Thursday: null, Friday: null, Saturday: null, Sunday: null }

    function FillFormData(weekday, model) {
        listHa[weekday] = model;
        if (model.IdJornada == -1 || model.Festivo) {
            $button = $('#button' + weekday + "Crear").dxButton('instance').option('disabled', true);
            $button = $('#button' + weekday + "PC").dxButton('instance').option('disabled', true);
        }
        else {
            $button = $('#button' + weekday + "Crear").dxButton('instance').option('disabled', false);
            $button = $('#button' + weekday + "PC").dxButton('instance').option('disabled', false);
        }
    }
    function buildButton(date, buttonText, buttonName, buttonHint, tipoRegistro) {
        var day = date.date.getDay() || 7; // Get current day number, converting Sun. to 7
        if (day == 1) {
            lunesSemana = date.date.toLocaleDateString('es-ES');
            setValExtDate("cevalweekdate", date.date);
            setValExtDate("ceExtConfDate", date.date);
        }
        // Genera el botón
        if (0 != 1)
        {
            var nombre = date.date.toLocaleDateString('en', { weekday: 'long' });
            $item = $("<div id='button" + nombre + buttonName + "' style='margin-left: 3px;display:none'></div>");
            $item.dxButton({
                stylingMode: "contained",
                text: "", //buttonText,
                hint: buttonHint,                
                icon: (tipoRegistro == "JL") ? "clock" : ((tipoRegistro == "PC") ? "food" : "verticalaligncenter"),
                type: "success",
                onClick: function (e) {
                    let registro = null;
                    if (tipoRegistro == "JL") {
                        registro = ClonarObjeto(regDefaultJL);
                    }
                    else if (tipoRegistro == "PC") {
                        registro = ClonarObjeto(regDefaultPC);
                    }
                    else if (tipoRegistro == "HT"){
                        submitCrearHT(e);
                        return;
                    }
                    registro.Dia = date.date;
                    $('#calendario').dxScheduler('instance').showAppointmentPopup(registro);
                }
            });
            return $item;
        }
        return "";
    }

    function submitCrearHT(e) {
        $('#loadPanelPortal').dxLoadPanel('instance').show();
        value = $('#cevalweekdate').dxDateBox('instance').option('text');
        let diaSemana = 0;
        switch ($(e.element).attr("id")) {
            case "buttonTuesdayHT":
                diaSemana = 1;
                break;
            case "buttonWednesdayHT":
                diaSemana = 2;
                break;
            case "buttonThursdayHT":
                diaSemana = 3;
                break;
            case "buttonFridayHT":
                diaSemana = 4;
                break;
            case "buttonSaturdayHT":
                diaSemana = 5;
                break;
            case "buttonSundayHT":
                diaSemana = 6;
                break;
        }
        $.ajax({
            url: "https://t-worktime.t-systems.es/TWTime/Calendario/CrearHT",
            method: "POST",
            data: {
                lunes: value,
                idEmpleado: 15612,
                diaSemana: diaSemana
            }
        })
        .done(function (s, e) {
            $('#loadPanelPortal').dxLoadPanel('instance').hide();           
            $("#calendario").dxScheduler("instance").getDataSource().reload();
        })
        .fail(function (s, e) {
            $('#loadPanelPortal').dxLoadPanel('instance').hide();
            dataSourceLoadErrorGeneral(s.responseText);
        });
    }

    function buildButtonInfo(date) {
        // Genera el botón
        var nombre = date.date.toLocaleDateString('en', { weekday: 'long' });
        let strInfo = "#button_info" + nombre;
        $item = $("<div id='button_info" + nombre + "'></div>");
        $item.dxButton({
            stylingMode: "contained",
            icon: "info",
            type: "normal",
            hint: "Datos del día para el computo de horas",
            onClick: function () {
                $.getJSON("https://t-worktime.t-systems.es/TWTime/Calendario/GetDatosDiaEmp" + "?dia=" + encodeURIComponent(date.date.ToLocalDateStringMVC()) + "&IdEmpleado=15612")
                    .done(function (s) {
                        let tpInfo = $('#tpInfDia').dxTooltip('instance');
                        tpInfo.option("target", strInfo);
                        tpInfo.show();
                        $('#tpInfDiaCont').html(s);
                    })
                    .fail(function (s, e, e2) {
                        dataSourceLoadErrorGeneral(s.responseText);
                    });
            }
        });
        return $item;
    }

    function setValExtDate(id, date) {
        let objDate = $('#' + id).dxDateBox('instance');
        if (objDate != undefined) {
            $('#' + id).dxDateBox('instance').option('value', date);
        }
    }
    function isEditable(data) {

                    if ($('#calendario').dxScheduler('instance').option("currentView") == "month" || $('#calendario').dxScheduler('instance').option("currentView").toLowerCase() == "vista mensual") {
                    calEditable = false;
                    return false;
                }

                if (data == undefined) {
                    calEditable = false;
                    return false;
                }

                var date = new Date(data.Dia ?? data.startDate);
                var editable = !isFestivo(date);
                if (data.Editable != undefined) {
                    editable = editable && data.Editable;
                }
                else {
                    editable = calEditable;
                    return editable;
                }
                    calEditable = editable;
        return editable;
    }
    function isFestivoTrabajable(date) {
        var found = false;
        context.Agenda.forEach(
            function (item) {
                if (new Date(item.Dia).Compare(date)) {
                    found = true && item.Trabajable;
                    return;
                }
            }
        );
        return found;
    }
    function isFestivo(date) {
        var found = false;
        context.Agenda.forEach(
            function (item) {
                if (new Date(item.Dia).Compare(date)) {
                    found = true;
                    return;
                }
            }
        );
        return found;
    }
    // Mostrar celda hora día semana
    function dataCellTemplate(itemData, itemIndex, itemElement) {
        var element = $('<div />');
        var date = new Date(itemData.date ?? itemData.startDate);
        if (isFestivo(date)) {
            element.addClass('disable-date');
        }

        return itemElement.append(element);
    }
    // Mostrar celda hora día semana
    function MonthdataCellTemplate(itemData, itemIndex, itemElement) {
        var date = new Date(itemData.date ?? itemData.startDate);
        if (isFestivo(date)) {
            itemElement.addClass('disable-date');
        }
        var element = $('<div>' + itemData.text + '</div>');

        return itemElement.append(element);
    }

    function dateCellTemplateMonth(itemData) {
        $dateTemplate = $('<span class="dx-scheduler-header-panel-cell-date"></span>');
        var weekDay = `${itemData.date.toLocaleDateString('es-ES', { weekday: 'short' })}.` ;
        $dateTemplate.html(weekDay);
        return $dateTemplate;
    }

    // Mostrar título día de la semana
    var dateCellTemplate = function (itemData) {
        $dateTemplate = $("<span class='dx-scheduler-header-panel-cell-date calendarioHeader' style='text-transform: capitalize;'></span>")
        var weekDay = itemData.date.toLocaleDateString('es-ES', { weekday: 'long', day: 'numeric' });
        var date = new Date(itemData.date ?? itemData.startDate);
        if (isFestivo(date)) {
            $dateTemplate.addClass('disable-date');
        }
        $dateTemplate.append(weekDay);

        $divContDia = $("<div class='col-12 container-buttons' cabBotdia></div>");
        $dateTemplate.append($divContDia);
        if (isEditable)
        {
            $divContDia.append(buildButton(itemData, `JL`, "Crear", `Crear registro jornada laboral`, "JL"));
            $divContDia.append(buildButton(itemData, `PC`, "PC", `Crear registro pausa comida`, "PC"));
            $divContDia.append(buildButton(itemData, `HT`, "HT", `Crear registros horario teórico`, "HT"));
        }
        $dateTemplate.append(buildContadorHoras(itemData));
        $('div[aria-label="clock"]').each(function() {
                    $(this).css('display', 'none');
                });
                $('div[aria-label="food"]').each(function() {
                    $(this).css('display', 'none');
                });
        return $dateTemplate;
    }
    function buildContadorHoras(date) {
        var day = date.date.getDay() || 7; // Get current day number, converting Sun. to 7
        $itemDiv = $("<div style='display: flex; margin: 0px auto;'></div>");
        // Genera el botón
        var nombre = date.date.toLocaleDateString('en', { weekday: 'long' });
        $item = $("<div style='font-size: 14px;'>H. reg.: <span id='contador" + nombre + "' pausas=\"\">00:00</span></div>");
        $itemDiv.append($item);
        $itemDiv.append(buildButtonInfo(date));
        return $itemDiv;
    }
</script>

<script>
    var calEditable;
    function onFormOpening(data) {
        data.cancel = !isEditable(data.appointmentData);
        if (data.cancel) {
            data.popup.hide();
            calEditable = false;
        }
        else {
            calEditable = true;
            var form = data.form;
            form.option("items", [
                {
                    name: "Dia",
                    dataField: "Dia",
                    label: {
                        text: `Día`
                    },
                    editorType: "dxDateBox",
                    colSpan: 2,
                    editorOptions: {
                        readOnly: true,
                        hoverStateEnabled: false,
                        disable: true,
                        type: "date"
                    }
                },
                {
                    name: "Descripcion",
                    dataField: "Descripcion",
                    label: {
                        text: `Descripción`
                    },
                    editorType: "dxTextBox",
                    colSpan: 2,
                    editorOptions: {
                        readOnly: true,
                        hoverStateEnabled: false,
                        disable: true,
                    }
                },
                {
                    name: "StartDate",
                    dataField: "StartDate",
                    label: {
                        text: `Hora inicio`
                    },
                    editorType: "dxDateBox",
                    editorOptions: {
                        width: "100%",
                        type: "time"
                    }
                },
                {
                    name: "EndDate",
                    dataField: "EndDate",
                    label: {
                        text: `Hora fin`,
                    },
                    editorType: "dxDateBox",
                    editorOptions: {
                        width: "100%",
                        type: "time"
                    }
                }

            ]);
            form.option("labelMode", "static");
            form.option("LabelLocation", "left");

            var toolbarButtons = data.popup.option("toolbarItems");
            toolbarButtons[0].options.text = `Aceptar`;
            toolbarButtons[0].options.type = "success";
            data.popup.option("toolbarItems", toolbarButtons);
        }
    }
</script>

<script>
    var e;
    // Menu item template
    var ItemMenuContextTemplate = function (itemData) {
        var $template = $('<div></div>');

        if (itemData.Color) {
            $template.append("<div class= 'Appointment-badge' style = 'background-color:" + itemData.Color + ";' ></div >");
        }

        $template.append(itemData.Text);
        return $template;
    }

    function onAppointmentContextMenu(e) {
        var contextMenuInstance = $("#context-menu").dxContextMenu("instance");
        let ds = JSON.parse(`[{"Text":"Abrir","BeginGroup":false,"Disabled":false,"OnItemClick":showAppointment},{"Text":"Eliminar","BeginGroup":false,"Disabled":false,"OnItemClick":deleteAppointment}]`);
        var date = new Date(e.appointmentData.Dia);
        if (!e.appointmentData.Editable && !isFestivoTrabajable(date)) {
            ds = [];
        }
        contextMenuInstance.option({
            dataSource: ds,
            target: ".dx-scheduler-Appointment",
            onItemClick: onItemClick(e),
            disabled: false,
        });
    }

    function onCellContextMenu(e) {
        var contextMenuInstance = $("#context-menu").dxContextMenu("instance");
        contextMenuInstance.option({
            dataSource: JSON.parse(`[]`),
            target: ".dx-scheduler-date-table-cell",
            onItemClick: onItemClick(e),
            disabled: false,
        });
    }

    var onContextMenuHiding = function (e) {
        e.component.option({
            disabled: true,
            dataSource: []
        })
    }

    var onItemClick = function (contextMenuEvent) {
        return function (e) {
            e.itemData.OnItemClick(contextMenuEvent, e);
        }
    }

    //var createAppointment = function (e) {
    //    let appointment = e.appointmentData ?? e.newData;
    //    if (isEditable(appointment)) {
    //        e.component.showAppointmentPopup({
    //            StartDate: e.cellData.startDate
    //        }, true);
    //    }
    //};

    var showCurrentDate = function (e) {
        e.component.option("currentDate", new Date());
    };

    var showAppointment = function (s, e) {
        let appointment = s.appointmentData ?? s.newData;
        s.cancel = !isEditable(appointment);        
    };

    var deleteAppointment = function (e) {
        let appointment = e.appointmentData ?? e.newData;
        appointment = appointment.appointmentData ?? appointment;
        e.cancel = !isEditable(appointment);
        if (!e.cancel && e.event && e.event.type == "dxcontextmenu")
            e.component.deleteAppointment(appointment);
    };

    function getRegistroById(id) {
        let registros = $('#calendario').dxScheduler('instance').getDataSource().items();
        return DevExpress.data.query(registros)
            .filter("Id", id)
            .toArray()[0];
    }
    
</script>

<script>

    function disableDblClick(e) {
        let appointment = e.appointmentData ?? e.newData;
        e.event.preventDefault(!isEditable(appointment));
        e.cancel = !isEditable(appointment);
    }

    function verificarEstado(e) {
        let weekday = e.element[0].id.replace("popup", "");
        var model = listHa[weekday];
        let form = $('#form' + weekday).dxForm('instance');
        form.updateData(model);
        if (!calEditable) {
            e.component.hide();
        }
    }

    function submitLeyendas(e) {
        $('#tpLeyenda').dxTooltip('instance').show();
    }
</script><style>
    .calendarioHeader {
        display: flex !important;
        flex-direction: column !important;
    }

    .dx-scheduler-appointment-content {
        color: black;
        font-weight: 700;
        font-size: 16px;
    }

    .dx-scheduler-appointment-content-details {
        font-size: 13px;
    }

    .container-buttons {
        display: inline-block;
    }

        .container-buttons .dx-button-text {
            font-size: 14px;
        }

    -moz-document url-prefix() 
    {
        .dx-scheduler-work-space-month .dx-scheduler-date-table-cell {
            position: relative;
        }

        .dx-scheduler-work-space-month .dx-scheduler-date-table-cell .disable-date {
            position: absolute;
            width: 100%;
            height: 100%;
        }
    }

    .disable-date, .dinner {
        height: 100%;
    }

    .disable-date {
        background-image: repeating-linear-gradient(135deg, rgba(244, 67, 54, 0.1), rgba(244, 67, 54, 0.1) 4px, transparent 4px, transparent 9px);
        color: #9B6467;
    }

    .dx-scheduler-header-panel-cell .disable-date {
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    .dinner {
        background: rgba(255, 193, 7, 0.2);
    }

    .dx-scheduler-time-panel-cell .dinner {
        color: rgba(255, 193, 7);
        font-weight: 400;
        background: transparent;
    }

    .dx-draggable {
        cursor: auto;
    }

    td.dx-scheduler-time-panel-cell .dinner .cafe {
        height: 200%;
        width: 100%;
        left: 50%;
        -webkit-mask: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 24 24" width="24"><path d="M0 0h24v24H0V0z" fill="none" /><path d="M20 3H4v10c0 2.21 1.79 4 4 4h6c2.21 0 4-1.79 4-4v-3h2c1.11 0 2-.9 2-2V5c0-1.11-.89-2-2-2zm0 5h-2V5h2v3zM4 19h16v2H4z" /></svg>');
        -webkit-mask-repeat: no-repeat;
        -webkit-mask-position-y: 50%;
        -webkit-mask-position-x: 100%;
        margin-top: -4px;
        background-color: #ffc107;
    }

    .dx-scheduler-date-table-cell {
        padding: 0;
        opacity: 1;
    }


    .ceFormText {
        margin: 15px;
    }

    .ceFormText form {
        display: flex;
        column-gap: 10px;
    }

    div.col-12.container-buttons .dx-icon {
        color: white !important;
    }

    

</style>

                        </div>
                        <div class="content-block" style="padding: 0% 0px 2% 0px;">
                            <div class="content-footer">
                                <div id="footer">
                                    Copyright © 2025 T-WorkTime. Versión 2.0.0.1
                                    <br />
                                    All trademarks or registered trademarks are property of their respective owners. T-WorkTime
                                </div>
                            </div>
                        </div>
                    </div>
                    <footer style="display: none; position: fixed; bottom: 0; width: 100%; height: 4%; background-color: rgb(242, 242, 242); align-items: center; border-color: rgb(207, 207, 207) !important; border-top: 1px solid; ">
                            
                    </footer>
                </div><script>DevExpress.utils.readyCallbacks.add((function($){$("#layout-drawer").dxDrawer({"position":"left","opened":true,"template":$("#navigation-menu"),"closeOnOutsideClick":true,"onOptionChanged":onOptionChangeDrawerMenu});}).bind(this, jQuery));</script>
        </div>
    </div>

<script type="text/html" id="navigation-menu">        <div class="menu-container dx-swatch-additional">


<%!function(){%><div id="<%=arguments[0]%>"></div><%DevExpress.aspnet.createComponent("dxTreeView",{"dataSource":{"store":new DevExpress.data.ArrayStore({"data":[{"items":[{"items":[],"id":"mnuRegistroCalendarioEmp","text":"Calendario","icon":"spinright","expanded":false,"selected":false,"DisplayName":"mnuRegistroCalendarioEmp","Url":"TwTime/Calendario/CalendarioEmpleado","Perfiles":"40","Roles":"","Usuarios":"","recursoManager":{"BaseName":"PortalTSEExpressCore.Resources.Resource","IgnoreCase":false,"ResourceSetType":"System.Resources.RuntimeResourceSet, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e"}},{"items":[],"id":"mnuConsultaSemanaPteValidar","text":"Consulta semanas pte. validar","icon":"spinright","expanded":false,"selected":false,"DisplayName":"mnuConsultaSemanaPteValidar","Url":"TwTime/SemanaPteValidar/SemanaPteValidar","Perfiles":"40","Roles":"","Usuarios":"","recursoManager":{"BaseName":"PortalTSEExpressCore.Resources.Resource","IgnoreCase":false,"ResourceSetType":"System.Resources.RuntimeResourceSet, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e"}},{"items":[],"id":"mnuTworkTimeV1","text":"Acceso a T-Worktime V1","icon":"spinright","expanded":false,"selected":false,"DisplayName":"mnuTworkTimeV1","Url":"https://t-worktimev1.t-systems.es","Perfiles":"40","Roles":"","Usuarios":"","recursoManager":{"BaseName":"PortalTSEExpressCore.Resources.Resource","IgnoreCase":false,"ResourceSetType":"System.Resources.RuntimeResourceSet, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e"}}],"id":"mnuRegistroHorarios","text":"Registro de Jornada","icon":"event","expanded":false,"selected":false,"DisplayName":"mnuRegistroHorarios","Url":"#","Perfiles":"39;40;41","Roles":"","Usuarios":"","recursoManager":{"BaseName":"PortalTSEExpressCore.Resources.Resource","IgnoreCase":false,"ResourceSetType":"System.Resources.RuntimeResourceSet, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e"}}]})},"dataStructure":"tree","expandEvent":"click","selectionMode":"single","expandedExpr":"expanded","selectedExpr":"selected","noDataText":"No tiene permisos para acceder","focusStateEnabled":true,"width":340.0,"onContentReady":PortalAspNetCoreApp1.onTreeViewReady,"onItemClick":PortalAspNetCoreApp1.onTreeViewItemClick},arguments[0])%><%}("menuTreeView")%>
System.Threading.Tasks.Task`1[System.Threading.Tasks.VoidTaskResult]        </div>
</script>    <script>
        function ActivarPanelPortal(mensaje) {
            let loadPanel = $("#loadPanelPortal").dxLoadPanel("instance");
            loadPanel.message = mensaje;
            loadPanel.show();
        }

        function DesactivarPanelPortal() {
            let loadPanel = $("#loadPanelPortal").dxLoadPanel("instance");
            loadPanel.hide();
        }
    </script>

    <script>
        function onOptionChangeDrawerMenu(s, e) {

            if (s.name === "opened") {
                if (s.value) {
                    PortalAspNetCoreApp1.onTreeViewReady($('#menuTreeView').data().dxTreeView, null);
                    if ($(buttonMenu.element).dxButton('instance').option("icon") === "close") {
                        $(buttonMenu.element).dxButton('instance').option("icon", "menu");
                    }
                    else {
                        $(buttonMenu.element).dxButton('instance').option("icon", "close");
                    }
                }
                else {
                    $('#menuTreeView').data().dxTreeView.collapseAll();
                    if ($(buttonMenu.element).dxButton('instance').option("icon") === "close") {
                        $(buttonMenu.element).dxButton('instance').option("icon", "menu");
                    }
                    else {
                        $(buttonMenu.element).dxButton('instance').option("icon", "close");
                    }
                }
            }
        }

        var buttonMenu;
        function MenuInicializado(s) {
            buttonMenu = s;
        }

        document.addEventListener("DOMContentLoaded", function documentReady() {
            this.removeEventListener("DOMContentLoaded", documentReady);
            PortalAspNetCoreApp1.init();
        });

    </script>

    <script>

        DiccionarioPortal = AsignarIdiomaDiccionario('es-ES'.substring(0, 2));
        var idiomaApp = 'es';
        //Globalize.locale(idiomaApp);
        DevExpress.localization.locale("es-ES");


        //DevExpress.localization.locale(idiomaApp)
        if (idiomaApp == "es" || idiomaApp == "ca") {
            DevExpress.config({ defaultCurrency: "EUR" });
        }
        else {
            DevExpress.config({ defaultCurrency: "GBP" });
        }

        DevExpress.config({
            editorStylingMode: "outlined",
            editorLabelMode: "floating"
        });
        function onError(e) {

            tratarMensajeErrorGrid(e);
            DesactivarPanelPortal();
        }

        function dataSourceLoadError(e) {
            tratarMensajeErrorGrid(e);
        }

        function dataSourceLoadErrorGeneral(e) {
            tratarMensajeErrorDataSource(e);
        }

        function helpBtnClick(e) {
            let urlAyuda = `https://yam-united.telekom.com/pages/worktime-iberia-support/apps/content/home`;
            if (urlAyuda != '') {
                window.open(urlAyuda, "_blank");
            }
        }

        function soporteBtnClick(e) {
            window.open("https://apps.t-systems.es/portal/#/dashboard/home?redirectTo=/dashboard/ticket-request?openNewTicket=true&idApp=11", "_blank");
        }

        let pathAvatar = `images/Avatares/default.png`;
        if (pathAvatar != "") {
            localStorage.setItem("pathAvatar", pathAvatar);
        }

        function getAvatar() {
            if (localStorage.getItem("pathAvatar") != undefined && localStorage.getItem("pathAvatar") != null) {
                this.onload = null;
                $("#fotoAvatar").attr("src", localStorage.getItem("pathAvatar"));
            }
        }
    </script>
</body>

</html>
