# T-Worktime Assistant Extension Testing Guide

## Pre-Testing Checklist

- [ ] All extension files are present in the project directory
- [ ] Icons have been generated (or placeholder PNGs are available)
- [ ] Chrome/Edge browser is available for testing
- [ ] Access to T-Worktime system for testing

## Testing Steps

### 1. Extension Loading Test

1. Open Chrome/Edge browser
2. Navigate to `chrome://extensions/` or `edge://extensions/`
3. Enable "Developer mode" (toggle in top-right)
4. Click "Load unpacked"
5. Select the project directory
6. **Expected Result**: Extension appears in the list without errors

**✅ Pass / ❌ Fail**: ___________

### 2. Extension Icon Test

1. Look for the T-Worktime Assistant icon in the browser toolbar
2. Click the extension icon
3. **Expected Result**: Popup opens showing the T-Worktime Assistant interface

**✅ Pass / ❌ Fail**: ___________

### 3. T-Worktime Page Detection Test

1. Navigate to `https://t-worktime.t-systems.es/TwTime/Calendario/CalendarioEmpleado`
2. Ensure you are logged in
3. Open the extension popup
4. Click "Load Week"
5. **Expected Result**: Employee ID and absent days are detected and displayed

**✅ Pass / ❌ Fail**: ___________

### 4. Week Selection Test

1. In the extension popup, change the week using the week picker
2. Click "Load Week" again
3. **Expected Result**: Interface updates for the selected week

**✅ Pass / ❌ Fail**: ___________

### 5. Time Configuration Test

1. Modify the default times (work hours and break times)
2. Click "Apply to All Days"
3. **Expected Result**: All working days show the new default times

**✅ Pass / ❌ Fail**: ___________

### 6. Individual Day Configuration Test

1. Manually change times for a specific day
2. **Expected Result**: Only that day's times are updated

**✅ Pass / ❌ Fail**: ___________

### 7. Absent Days Display Test

1. Check if absent days are properly marked
2. **Expected Result**: Absent days show "Absent Day" instead of time inputs

**✅ Pass / ❌ Fail**: ___________

### 8. Weekend Handling Test

1. Check Saturday and Sunday in the schedule
2. **Expected Result**: Weekends show "Weekend" instead of time inputs

**✅ Pass / ❌ Fail**: ___________

### 9. Data Submission Test (Careful!)

⚠️ **WARNING**: This will submit actual data to T-Worktime. Only test with a test week or data you're comfortable submitting.

1. Configure times for the week
2. Click "Submit All Entries"
3. **Expected Result**: Progress bar shows submission progress, results are displayed

**✅ Pass / ❌ Fail**: ___________

### 10. Error Handling Test

1. Try using the extension without being on T-Worktime page
2. Try loading week data when not logged in
3. **Expected Result**: Clear error messages are displayed

**✅ Pass / ❌ Fail**: ___________

## Console Testing

### Browser Console Checks

1. Open browser DevTools (F12)
2. Check Console tab for any errors during extension use
3. **Expected Result**: No critical errors, only informational logs

**Errors Found**: ___________

### Extension Popup Console

1. Right-click extension icon → "Inspect popup"
2. Use the extension while monitoring console
3. **Expected Result**: Proper logging of actions and data

**Errors Found**: ___________

### Content Script Console

1. On T-Worktime page, open DevTools
2. Use extension features while monitoring console
3. **Expected Result**: Context extraction logs, no errors

**Errors Found**: ___________

## Performance Testing

### Load Time Test

1. Measure time from clicking extension icon to popup fully loaded
2. **Expected Result**: < 2 seconds

**Time**: ___________

### Data Extraction Test

1. Measure time from clicking "Load Week" to data displayed
2. **Expected Result**: < 5 seconds

**Time**: ___________

### Submission Speed Test

1. Time a full week submission (if testing with real data)
2. **Expected Result**: Reasonable progress with feedback

**Time**: ___________

## Edge Cases Testing

### Different Week Scenarios

- [ ] Current week
- [ ] Past week
- [ ] Future week
- [ ] Week with holidays
- [ ] Week with many absent days

### Different Time Scenarios

- [ ] Standard work hours (09:00-18:00)
- [ ] Early start (07:00-16:00)
- [ ] Late start (10:00-19:00)
- [ ] Split shifts
- [ ] No break time
- [ ] Long break time

### Browser Scenarios

- [ ] Chrome (latest version)
- [ ] Edge (latest version)
- [ ] Different screen sizes
- [ ] Extension popup resizing

## Final Checklist

- [ ] All core features work as expected
- [ ] No critical errors in any console
- [ ] User interface is responsive and clear
- [ ] Error messages are helpful
- [ ] Data submission works correctly
- [ ] Extension can be safely used in production

## Notes

**Issues Found**:
___________

**Suggestions for Improvement**:
___________

**Overall Assessment**: ✅ Ready for Use / ⚠️ Needs Minor Fixes / ❌ Needs Major Work
