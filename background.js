/**
 * T-Worktime Assistant Background Service Worker
 * Handles API calls and background tasks
 */



// This function will be injected and executed in the T-Worktime page context
async function executeWorktimeAPI(entryDate, startTime, endTime, entryType, employeeId) {
    try {
        // Check if the sendWorktimeEntry function is available
        if (typeof sendWorktimeEntry !== 'function') {
            // If not available, define it inline (simplified version)
            window.sendWorktimeEntry = async function(entryDate, startTime, endTime, entryType, employeeId = 15612) {
                const baseValues = {
                    "Id": -9999,
                    "IdCalEmpDia": 0,
                    "IdCalEmp": 0,
                    "IdCompensacion": 0,
                    "IdJornada": null,
                    "IdTipoRegistro": entryType === 'break' ? 6 : 1,
                    "IdEmpleado": employeeId,
                    "Activo": false,
                    "Year": 0,
                    "HoraInicio": "00:00",
                    "PausaTotal": null,
                    "PausaTotalHoras": 0,
                    "HoraFin": "00:00",
                    "Text": entryType === 'break' ? "PC" : "TR",
                    "StartDate": `1980-01-01T${startTime}:00`,
                    "HoraInicioTime": "00:00:00",
                    "HoraFinTime": "00:00:00",
                    "EndDate": `1980-01-01T${endTime}:00`,
                    "Dia": "",
                    "It": false,
                    "Festivo": false,
                    "FEsp": false,
                    "Descripcion": entryType === 'break' ? "Pausa de comida" : "Trabajo",
                    "Color": entryType === 'break' ? "#FF6600" : "#0066FF",
                    "AllDay": false,
                    "Recurrence": null,
                    "RecurrenceException": null,
                    "RecurrenceRule": null,
                    "StartTimezone": null,
                    "EndTimezone": null,
                    "Trabajable": true,
                    "Validado": false,
                    "Cerrado": false,
                    "Bloqueado": false,
                    "Falta": false,
                    "Incidencia": false,
                    "Observaciones": null,
                    "Fcre": "0001-01-01T00:00:00",
                    "Fmod": "0001-01-01T00:00:00"
                };

                // Update Dia field
                const dateObj = new Date(entryDate + 'T00:00:00Z');
                dateObj.setDate(dateObj.getDate() + 1);
                baseValues.Dia = dateObj.toISOString().replace('T00:00:00.000Z', 'T22:00:00.000Z');

                // Create form data
                const formData = new URLSearchParams();
                formData.append('values', JSON.stringify(baseValues));

                const response = await fetch("https://t-worktime.t-systems.es/TWTime/Calendario/UpdateCalendarioEmpleado", {
                    method: "PUT",
                    headers: {
                        "accept": "text/plain, */*; q=0.01",
                        "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
                        "x-requested-with": "XMLHttpRequest"
                    },
                    body: formData,
                    credentials: "include"
                });

                return response;
            };
        }

        // Call the API function
        const response = await sendWorktimeEntry(entryDate, startTime, endTime, entryType, employeeId);

        if (response.ok) {
            return {
                success: true,
                message: `${entryType} entry submitted successfully for ${entryDate}`
            };
        } else {
            const errorText = await response.text();
            return {
                success: false,
                message: `API call failed: ${response.status} ${errorText}`
            };
        }

    } catch (error) {
        console.error('Error in executeWorktimeAPI:', error);
        return {
            success: false,
            message: error.message
        };
    }
}

class WorktimeBackground {
    constructor() {
        this.init();
    }

    init() {
        // Listen for messages from popup and content scripts
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.handleMessage(request, sender, sendResponse);
            return true; // Keep message channel open for async responses
        });

        // Handle extension installation
        chrome.runtime.onInstalled.addListener((details) => {
            if (details.reason === 'install') {
                console.log('T-Worktime Assistant installed');
                this.showWelcomeNotification();
            }
        });
    }

    async handleMessage(request, sender, sendResponse) {
        try {
            switch (request.action) {
                case 'submitEntry':
                    const result = await this.submitWorktimeEntry(request.entry, request.employeeId);
                    sendResponse(result);
                    break;

                case 'getEmployeeData':
                    const employeeData = await this.getEmployeeDataFromTab(sender.tab.id);
                    sendResponse(employeeData);
                    break;

                case 'checkWorktimePage':
                    const pageInfo = await this.checkWorktimePage(sender.tab.id);
                    sendResponse(pageInfo);
                    break;

                default:
                    sendResponse({ success: false, error: 'Unknown action' });
            }
        } catch (error) {
            console.error('Error handling message:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    async submitWorktimeEntry(entry, employeeId) {
        try {
            console.log('Submitting worktime entry:', entry);

            // Get the active tab to execute the API call
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab.url.includes('t-worktime.t-systems.es')) {
                throw new Error('Not on T-Worktime website');
            }

            // Execute the sendWorktimeEntry function in the context of the page
            const results = await chrome.scripting.executeScript({
                target: { tabId: tab.id },
                function: executeWorktimeAPI,
                args: [entry.date, entry.startTime, entry.endTime, entry.type, employeeId]
            });

            if (results && results[0] && results[0].result) {
                const result = results[0].result;
                console.log('API call result:', result);

                return {
                    success: result.success,
                    message: result.message || 'Entry submitted successfully'
                };
            } else {
                throw new Error('No result from API call');
            }

        } catch (error) {
            console.error('Error submitting worktime entry:', error);
            return {
                success: false,
                message: error.message
            };
        }
    }

    async getEmployeeDataFromTab(tabId) {
        try {
            const results = await chrome.scripting.executeScript({
                target: { tabId: tabId },
                function: () => {
                    // This will be executed in the content script context
                    if (window.worktimeContent) {
                        return window.worktimeContent.getEmployeeData();
                    } else {
                        return { success: false, error: 'Content script not available' };
                    }
                }
            });

            return results[0]?.result || { success: false, error: 'No result from content script' };
        } catch (error) {
            console.error('Error getting employee data from tab:', error);
            return { success: false, error: error.message };
        }
    }

    async checkWorktimePage(tabId) {
        try {
            const results = await chrome.scripting.executeScript({
                target: { tabId: tabId },
                function: () => {
                    return {
                        isWorktimePage: window.location.hostname.includes('t-worktime.t-systems.es'),
                        url: window.location.href,
                        title: document.title
                    };
                }
            });

            return results[0]?.result || { isWorktimePage: false };
        } catch (error) {
            console.error('Error checking worktime page:', error);
            return { isWorktimePage: false, error: error.message };
        }
    }

    showWelcomeNotification() {
        // Show a welcome notification when the extension is first installed
        if (chrome.notifications) {
            chrome.notifications.create({
                type: 'basic',
                iconUrl: 'icons/icon48.png',
                title: 'T-Worktime Assistant',
                message: 'Extension installed! Navigate to T-Worktime and click the extension icon to get started.'
            });
        }
    }

    // Utility method to log messages with timestamp
    log(message, data = null) {
        const timestamp = new Date().toISOString();
        console.log(`[${timestamp}] T-Worktime Assistant:`, message, data || '');
    }
}

// Initialize the background service
const worktimeBackground = new WorktimeBackground();
