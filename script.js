/**
 * T-Worktime API Integration Library
 * Creates and sends fetch requests to update the worktime calendar.
 */

/**
 * Creates and sends a fetch request to update the worktime calendar.
 *
 * @param {string} entryDate - The date of the entry in "YYYY-MM-DD" format.
 * @param {string} startTime - The start time of the entry in "HH:MM" format.
 * @param {string} endTime - The end time of the entry in "HH:MM" format.
 * @param {string} entryType - The type of entry, either "break" or "worktime".
 * @param {number} [employeeId=15612] - The ID of the employee.
 * @returns {Promise<Response>} A promise that resolves with the fetch response.
 */
 async function sendWorktimeEntry(entryDate, startTime, endTime, entryType, employeeId = 15612) {
    // --- 1. Construct the 'values' object ---
    const baseValues = {
      "Id": -9999,
      "IdCalEmpDia": 0,
      "IdCalEmp": 0,
      "IdCompensacion": 0,
      "IdJornada": null,
      "IdTipoRegistro": 6, // Defaulting to 6 (Pausa de comida), will be overridden for worktime.
      "IdEmpleado": employeeId,
      "Activo": false,
      "Year": 0,
      "HoraInicio": "00:00", // This seems to be a placeholder in the sample, StartDate/EndDate are more specific.
      "PausaTotal": null,
      "PausaTotalHoras": 0,
      "HoraFin": "00:00", // Placeholder
      "Text": "PC", // To be updated based on entryType
      "StartDate": "1980-01-01T13:00:00", // To be updated
      "HoraInicioTime": "00:00:00", // Placeholder
      "HoraFinTime": "00:00:00", // Placeholder
      "EndDate": "1980-01-01T14:00:00", // To be updated
      "Dia": "2025-05-21T22:00:00.000Z", // To be updated
      "It": false,
      "Festivo": false,
      "FEsp": false,
      "Descripcion": "Pausa de comida", // To be updated
      "Timestamp": "0001-01-01T00:00:00+00:00",
      "FechaValEmp": null,
      "FechaValRRHH": null,
      "CanValidate": true,
      "Editable": true,
      "Codigo": "PC", // To be updated
      "Color": "PC", // To be updated
      "CodigoColor": null,
      "Ocupacion": 0,
      "FecIniComp": "0001-01-01T00:00:00",
      "FecFinComp": "0001-01-01T00:00:00",
      "IdCalendario": 0,
      "IsDeletable": false,
      "EsRevEmp": false,
      "EsValEmp": false,
      "TotalHorasSinDescanso": 0,
      "TotalHorasSinDescansoTimeSpan": "00:00:00",
      "PausaTotalHorasTimeSpan": "00:00:00",
      "Turnos": null,
      "IsPartitionable": false,
      "Particionar": false,
      "AbsCod": null,
      "HorasFestRec": null,
      "SemanaActual": 0,
      "SemanaFutura": 0,
      "Ualta": 0,
      "UaltaNom": null,
      "UaltaLog": null,
      "Falta": "0001-01-01T00:00:00",
      "Umod": 0,
      "UmodNom": null,
      "UmodLog": null,
      "Fmod": "0001-01-01T00:00:00"
    };
  
    // Modify values based on parameters
    const updatedValues = { ...baseValues };
  
    // Update StartDate and EndDate
    // The year part is fixed as "1980-01-01" according to the problem description.
    updatedValues.StartDate = `1980-01-01T${startTime}:00`;
    updatedValues.EndDate = `1980-01-01T${endTime}:00`;
  
    // Update Dia: Use the input date, add 1 day, and keep the fixed time "T22:00:00.000Z"
    try {
      const dateObj = new Date(entryDate + 'T00:00:00Z'); // Parse as UTC to avoid timezone issues with date part
      if (isNaN(dateObj.getTime())) {
          throw new Error("Invalid entryDate format. Please use YYYY-MM-DD.");
      }
      dateObj.setUTCDate(dateObj.getUTCDate() - 1);
      const year = dateObj.getUTCFullYear();
      const month = String(dateObj.getUTCMonth() + 1).padStart(2, '0'); // Month is 0-indexed
      const day = String(dateObj.getUTCDate()).padStart(2, '0');
      updatedValues.Dia = `${year}-${month}-${day}T22:00:00.000Z`;
    } catch (error) {
      console.error("Error processing entryDate:", error);
      // Handle the error appropriately, e.g., by returning or throwing
      return Promise.reject(new Error("Invalid date for 'Dia' field processing."));
    }
  
  
    // Update fields based on entryType
    if (entryType === "break") {
      updatedValues.Descripcion = "Pausa de comida";
      updatedValues.Text = "PC";
      updatedValues.Codigo = "PC";
      updatedValues.Color = "PC";
      updatedValues.IdTipoRegistro = 6; // As per initial sample for "PC"
    } else if (entryType === "worktime") {
      updatedValues.Descripcion = "Jornada laboral";
      updatedValues.Text = "JL";
      updatedValues.Codigo = "JL";
      updatedValues.Color = "JL";
      updatedValues.IdTipoRegistro = 1; // Updated based on user feedback for "JL"
    } else {
      console.warn(`Unknown entryType: ${entryType}. Using default values for Descripcion, Text, Codigo, Color, and IdTipoRegistro.`);
      // Potentially throw an error or handle as needed
      return Promise.reject(new Error(`Unknown entryType: ${entryType}`));
    }
  
    // --- 2. Construct the request body ---
    // The body needs to be in 'application/x-www-form-urlencoded' format.
    // Example: "key=-9999&values=%7B%22Id%22%3A-9999%2C...%7D"
    // This means the JSON string for 'values' must be URL-encoded.
    const valuesJsonString = JSON.stringify(updatedValues);
    const requestBody = `key=${updatedValues.Id}&values=${encodeURIComponent(valuesJsonString)}`;
  
    // --- 3. Define headers ---
    const requestHeaders = {
      "accept": "text/plain, */*; q=0.01",
      "accept-language": "en-US,en;q=0.9", // You might want to make this configurable or remove if not strictly needed
      "cache-control": "no-cache",
      "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
      "pragma": "no-cache",
      "priority": "u=1, i", // This might be browser-specific and not strictly necessary for all fetch clients
      "sec-ch-ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"", // Browser specific
      "sec-ch-ua-mobile": "?0", // Browser specific
      "sec-ch-ua-platform": "\"Windows\"", // Browser specific
      "sec-fetch-dest": "empty",
      "sec-fetch-mode": "cors",
      "sec-fetch-site": "same-origin",
      "x-requested-with": "XMLHttpRequest" // Often used by servers to identify AJAX requests
    };
  
    // --- 4. Make the fetch call ---
    const url = "https://t-worktime.t-systems.es/TWTime/Calendario/UpdateCalendarioEmpleado";
  
    console.log("Sending fetch request with following details:");
    console.log("URL:", url);
    console.log("Method: PUT");
    console.log("Headers:", requestHeaders);
    console.log("Body (raw values object):", updatedValues);
    console.log("Body (encoded string):", requestBody);
  
  
    try {
      const response = await fetch(url, {
        method: "PUT",
        headers: requestHeaders,
        body: requestBody,
        mode: "cors", // As specified in the sample
        credentials: "include", // As specified in the sample
        referrer:
          "https://t-worktime.t-systems.es/TwTime/Calendario/CalendarioEmpleado", // Static from sample
        referrerPolicy: "strict-origin-when-cross-origin", // As specified
      });
  
      if (!response.ok) {
        // If the response is not OK (e.g., 4xx or 5xx status), log the error details.
        const errorText = await response.text();
        console.error(`Fetch failed with status: ${response.status} ${response.statusText}`);
        console.error("Error response body:", errorText);
        throw new Error(`Server responded with ${response.status}: ${errorText || response.statusText}`);
      }
  
      console.log("Fetch request successful!");
      // Depending on what the server returns, you might want to parse it (e.g., response.json() or response.text())
      // For "text/plain" accept header, response.text() is likely appropriate.
      const responseData = await response.text();
      console.log("Response data:", responseData);
      return response; // Or return responseData, depending on what the caller needs
  
    } catch (error) {
      console.error("Fetch request failed:", error);
      throw error; // Re-throw the error so the caller can handle it
    }
  }
  
  async function testSendEntry() {
    try {
      // Example for a worktime entry
      console.log("--- Testing WORKTIME entry ---");
      await sendWorktimeEntry("2025-06-02", "09:00", "13:00", "worktime", 15612);
      await sendWorktimeEntry("2025-06-02", "13:00", "14:00", "break", 15612);
      await sendWorktimeEntry("2025-06-02", "14:00", "18:00", "worktime", 15612);
      } catch (error) {
        console.error("Error in testSendEntry:", error.message);
        }
  }
        
  testSendEntry();
